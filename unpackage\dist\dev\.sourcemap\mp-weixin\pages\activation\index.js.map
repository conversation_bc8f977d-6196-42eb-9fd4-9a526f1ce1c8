{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/KingToyo/app_tixiaomeng/pages/activation/index.vue?641c", "webpack:///E:/KingToyo/app_tixiaomeng/pages/activation/index.vue?50fa", "webpack:///E:/KingToyo/app_tixiaomeng/pages/activation/index.vue?581d", "uni-app:///pages/activation/index.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/activation/index.vue?ca52", "webpack:///E:/KingToyo/app_tixiaomeng/pages/activation/index.vue?4b34"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "CategorySelector", "data", "showOverlay", "showFreeOverlay", "loading", "activationCode", "displayCode", "isFocused", "selectorVisible", "selectedCate<PERSON><PERSON>", "categoryData", "id", "name", "children", "filters", "methods", "closeOverlay", "showCategorySelector", "closeCategorySelector", "onCategoryConfirm", "console", "uni", "title", "icon", "duration", "onActivationCodeInput", "cleanValue", "formatActivationCode", "onInputFocus", "onInputBlur", "validateActivationCode", "onPractise", "confirmActivation", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onReachBottom", "onPullDownRefresh", "onBackPress", "onPageScroll", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,mTAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCmE9rB;EACAC;IACAC;EACA;EACAC;IAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACA;QACAC;QACAC;QACAC,WACA;UACAF;UACAC;UACAC,WACA;YACAF;YACAC;YACAC,WACA;cAAAF;cAAAC;YAAA,GACA;cAAAD;cAAAC;YAAA,GACA;cAAAD;cAAAC;YAAA,GACA;cAAAD;cAAAC;YAAA,GACA;cAAAD;cAAAC;YAAA;UAEA,GACA;YACAD;YACAC;YACAC,WACA;cAAAF;cAAAC;YAAA,GACA;cAAAD;cAAAC;YAAA;UAEA;QAEA,GACA;UACAD;UACAC;UACAC,WACA;YACAF;YACAC;YACAC,WACA;cAAAF;cAAAC;YAAA,GACA;cAAAD;cAAAC;YAAA;UAEA;QAEA;MAEA,GACA;QACAD;QACAC;QACAC,WACA;UACAF;UACAC;UACAC,WACA;YACAF;YACAC;YACAC,WACA;cAAAF;cAAAC;YAAA,GACA;cAAAD;cAAAC;YAAA;UAEA;QAEA;MAEA,GACA;QACAD;QACAC;QACAC,WACA;UACAF;UACAC;UACAC,WACA;YACAF;YACAC;YACAC,WACA;cAAAF;cAAAC;YAAA,GACA;cAAAD;cAAAC;YAAA;UAEA;QAEA;MAEA;IAEA;EAAA;EACAE;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACAC;;MAEA;MACA;QAAA;MAAA;MACAC;QACAC;QACAC;QACAC;MACA;MAEA;IACA;IACA;IACAC;MACA;;MAEA;MACA;;MAEA;MACA;QACAC;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACAT;UACAC;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAH;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAO;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;;MAEA;MACAX;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;IACA;EACA;EACA;EACAS,kCAEA;EACA;EACAC;IACA;EACA;EACA;EACAC,2BAEA;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;ACzTA;AAAA;AAAA;AAAA;AAAyyC,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACA7zC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activation/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activation/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=43e462dc&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=43e462dc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"43e462dc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activation/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=43e462dc&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-navbar/u-navbar\" */ \"@/uni_modules/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uGap: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-gap/u-gap\" */ \"@/uni_modules/uview-ui/components/u-gap/u-gap.vue\"\n      )\n    },\n    uOverlay: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-overlay/u-overlay\" */ \"@/uni_modules/uview-ui/components/u-overlay/u-overlay.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.activationCode.length\n  var g1 = _vm.activationCode.length\n  var g2 = _vm.activationCode.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showFreeOverlay = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"activation-content\">\r\n\t\t<u-navbar :placeholder=\"true\" bgColor=\"#FFD101\">\r\n\t\t\t<view class=\"u-nav-slot\" slot=\"left\">激活</view>\r\n\t\t</u-navbar>\r\n\t\t<u-gap height=\"50rpx\" bgColor=\"#FFD101\" />\r\n\t\t<view class=\"activation-box\">\r\n\t\t\t<text class=\"box-title\">请输入激活码</text>\r\n\t\t\t<view class=\"activation-input\"\r\n\t\t\t\t:class=\"{ 'input-focused': isFocused, 'input-filled': activationCode.length === 16 }\">\r\n\t\t\t\t<input class=\"input\" v-model=\"displayCode\" @input=\"onActivationCodeInput\" @focus=\"onInputFocus\"\r\n\t\t\t\t\t@blur=\"onInputBlur\" placeholder=\"请输入激活码\" maxlength=\"19\" type=\"text\" />\r\n\t\t\t\t<view class=\"input-counter\" :class=\"{ 'counter-complete': activationCode.length === 16 }\">{{\r\n\t\t\t\t\tactivationCode.length }}/16</view>\r\n\t\t\t</view>\r\n\t\t\t<text class=\"placeholder\">请输入16位激活码</text>\r\n\t\t\t<button class=\"u-reset-button button\" @click=\"showCategorySelector\" style=\"margin-bottom: 20rpx;\">选择内容</button>\r\n\t\t\t<button class=\"u-reset-button button\" @click=\"onPractise(1)\">激活</button>\r\n\t\t</view>\r\n\t\t<u-overlay :show=\"showOverlay\">\r\n\t\t\t<view class=\"overlay-content\">\r\n\t\t\t\t<view class=\"overlay-box\">\r\n\t\t\t\t\t<text class=\"box-title\">激活码：{{ displayCode }}</text>\r\n\t\t\t\t\t<text class=\"placeholder\">将激活以下内容</text>\r\n\t\t\t\t\t<view class=\"box-item\">\r\n\t\t\t\t\t\t<text class=\"value\">与豆伴匠配套-R系列</text>\r\n\t\t\t\t\t\t<text class=\"label\">有效期1年</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"u-reset-button submit-feedback\" @click=\"confirmActivation\">确定激活</button>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"close-icon\" @click=\"closeOverlay\">\r\n\t\t\t\t\t<u-icon name=\"close\" color=\"#000\" bold size=\"50rpx\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-overlay>\r\n\t\t<u-overlay :show=\"showFreeOverlay\">\r\n\t\t\t<view class=\"overlay-content\">\r\n\t\t\t\t<view class=\"overlay-box overlay-free-box\">\r\n\t\t\t\t\t<text class=\"box-free-title\">请选择激活内容</text>\r\n\t\t\t\t\t<view class=\"select-breadcrumb\">\r\n\t\t\t\t\t\t<text class=\"label\">已选：</text>\r\n\t\t\t\t\t\t<text class=\"value\">语文</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<scroll-view class=\"select-content\" scroll-y=\"true\" :scroll-top=\"scrollTop\">\r\n\t\t\t\t\t\t<view class=\"category-list\">\r\n\t\t\t\t\t\t\t<view class=\"category-li\" v-for=\"(item1, index1) in categoryData\" :key=\"index1\">{{ item1.name }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t<view class=\"\">\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"u-reset-button submit-feedback\" @click=\"confirmActivation\">确定激活</button>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"close-icon\" @click=\"showFreeOverlay = false\">\r\n\t\t\t\t\t<u-icon name=\"close\" color=\"#000\" bold size=\"50rpx\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-overlay>\r\n\t\t<!-- 分类选择器 -->\r\n\t\t<CategorySelector :visible=\"selectorVisible\" :data=\"categoryData\" @confirm=\"onCategoryConfirm\"\r\n\t\t\t@close=\"closeCategorySelector\" />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport CategorySelector from '@/components/CategorySelector.vue'\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t\tCategorySelector\r\n\t},\r\n\tdata: () => ({\r\n\t\tshowOverlay: false, // 是否显示激活码确认弹窗\r\n\t\tshowFreeOverlay: true, // 显示自由选择弹窗\r\n\t\tloading: true, // 加载状态\r\n\t\tactivationCode: '', // 激活码（纯数字字母，无空格）\r\n\t\tdisplayCode: '', // 显示的激活码（带空格格式化）\r\n\t\tisFocused: false, // 输入框是否聚焦\r\n\t\tselectorVisible: false, // 分类选择器显示状态\r\n\t\tselectedCategory: null, // 选中的分类\r\n\t\tcategoryData: [ // 分类数据\r\n\t\t\t{\r\n\t\t\t\tid: 1,\r\n\t\t\t\tname: '语文',\r\n\t\t\t\tchildren: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 11,\r\n\t\t\t\t\t\tname: '常规',\r\n\t\t\t\t\t\tchildren: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 111,\r\n\t\t\t\t\t\t\t\tname: '读写系列',\r\n\t\t\t\t\t\t\t\tchildren: [\r\n\t\t\t\t\t\t\t\t\t{ id: 1111, name: '一级目录内容' },\r\n\t\t\t\t\t\t\t\t\t{ id: 1112, name: '一级目录内容' },\r\n\t\t\t\t\t\t\t\t\t{ id: 1113, name: '一级目录内容' },\r\n\t\t\t\t\t\t\t\t\t{ id: 1114, name: '一级目录内容' },\r\n\t\t\t\t\t\t\t\t\t{ id: 1115, name: '一级目录内容' }\r\n\t\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 112,\r\n\t\t\t\t\t\t\t\tname: '基础系列',\r\n\t\t\t\t\t\t\t\tchildren: [\r\n\t\t\t\t\t\t\t\t\t{ id: 1121, name: '基础练习A' },\r\n\t\t\t\t\t\t\t\t\t{ id: 1122, name: '基础练习B' }\r\n\t\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 12,\r\n\t\t\t\t\t\tname: '专项',\r\n\t\t\t\t\t\tchildren: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 121,\r\n\t\t\t\t\t\t\t\tname: '阅读理解',\r\n\t\t\t\t\t\t\t\tchildren: [\r\n\t\t\t\t\t\t\t\t\t{ id: 1211, name: '现代文阅读' },\r\n\t\t\t\t\t\t\t\t\t{ id: 1212, name: '古诗文阅读' }\r\n\t\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tid: 2,\r\n\t\t\t\tname: '数学',\r\n\t\t\t\tchildren: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 21,\r\n\t\t\t\t\t\tname: '基础',\r\n\t\t\t\t\t\tchildren: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 211,\r\n\t\t\t\t\t\t\t\tname: '计算系列',\r\n\t\t\t\t\t\t\t\tchildren: [\r\n\t\t\t\t\t\t\t\t\t{ id: 2111, name: '加减法' },\r\n\t\t\t\t\t\t\t\t\t{ id: 2112, name: '乘除法' }\r\n\t\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tid: 3,\r\n\t\t\t\tname: '英语',\r\n\t\t\t\tchildren: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 31,\r\n\t\t\t\t\t\tname: '词汇',\r\n\t\t\t\t\t\tchildren: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tid: 311,\r\n\t\t\t\t\t\t\t\tname: '基础词汇',\r\n\t\t\t\t\t\t\t\tchildren: [\r\n\t\t\t\t\t\t\t\t\t{ id: 3111, name: '日常用语' },\r\n\t\t\t\t\t\t\t\t\t{ id: 3112, name: '学科词汇' }\r\n\t\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t]\r\n\t}),\r\n\tfilters: {},\r\n\tmethods: {\r\n\t\t// 关闭弹窗\r\n\t\tcloseOverlay() {\r\n\t\t\tthis.showOverlay = false\r\n\t\t},\r\n\r\n\t\t// 显示分类选择器\r\n\t\tshowCategorySelector() {\r\n\t\t\tthis.selectorVisible = true;\r\n\t\t},\r\n\r\n\t\t// 关闭分类选择器\r\n\t\tcloseCategorySelector() {\r\n\t\t\tthis.selectorVisible = false;\r\n\t\t},\r\n\r\n\t\t// 分类选择确认\r\n\t\tonCategoryConfirm(result) {\r\n\t\t\tthis.selectedCategory = result.finalSelection;\r\n\t\t\tconsole.log('选择的分类:', result);\r\n\r\n\t\t\t// 显示选择结果\r\n\t\t\tconst path = result.selectedPath.map(item => item.name).join(' - ');\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: `已选择: ${path}`,\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 3000\r\n\t\t\t});\r\n\r\n\t\t\tthis.closeCategorySelector();\r\n\t\t},\r\n\t\t// 激活码输入处理\r\n\t\tonActivationCodeInput(e) {\r\n\t\t\tlet value = e.detail.value;\r\n\r\n\t\t\t// 移除所有空格和非字母数字字符\r\n\t\t\tlet cleanValue = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();\r\n\r\n\t\t\t// 限制最大长度为16位\r\n\t\t\tif (cleanValue.length > 16) {\r\n\t\t\t\tcleanValue = cleanValue.substring(0, 16);\r\n\t\t\t}\r\n\r\n\t\t\t// 保存纯净的激活码\r\n\t\t\tthis.activationCode = cleanValue;\r\n\r\n\t\t\t// 格式化显示（每4位加一个空格）\r\n\t\t\tthis.displayCode = this.formatActivationCode(cleanValue);\r\n\t\t},\r\n\r\n\t\t// 格式化激活码显示\r\n\t\tformatActivationCode(code) {\r\n\t\t\t// 每4位添加一个空格\r\n\t\t\treturn code.replace(/(.{4})/g, '$1 ').trim();\r\n\t\t},\r\n\r\n\t\t// 输入框聚焦\r\n\t\tonInputFocus() {\r\n\t\t\tthis.isFocused = true;\r\n\t\t},\r\n\r\n\t\t// 输入框失焦\r\n\t\tonInputBlur() {\r\n\t\t\tthis.isFocused = false;\r\n\t\t},\r\n\r\n\t\t// 验证激活码格式\r\n\t\tvalidateActivationCode() {\r\n\t\t\tif (this.activationCode.length !== 16) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入16位激活码',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\t// 检查是否包含有效字符（字母和数字）\r\n\t\t\tconst validPattern = /^[A-Z0-9]{16}$/;\r\n\t\t\tif (!validPattern.test(this.activationCode)) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '激活码格式不正确',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\r\n\t\t\treturn true;\r\n\t\t},\r\n\r\n\t\t// 激活操作\r\n\t\tonPractise(e) {\r\n\t\t\t// 验证激活码\r\n\t\t\tif (!this.validateActivationCode()) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.showOverlay = true\r\n\t\t},\r\n\t\t// 确定激活\r\n\t\tconfirmActivation() {\r\n\t\t\tif (!this.validateActivationCode()) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 模拟激活成功\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '激活成功',\r\n\t\t\t\ticon: 'success',\r\n\t\t\t\tduration: 2000\r\n\t\t\t});\r\n\t\t\tthis.activationCode = '' // 激活码（纯数字字母，无空格）\r\n\t\t\tthis.displayCode = '' // 显示的激活码（带空格格式化）\r\n\t\t\tthis.closeOverlay();\r\n\t\t},\r\n\t},\r\n\t// 页面周期函数--监听页面加载\r\n\tonLoad(options) {\r\n\r\n\t},\r\n\t// 页面周期函数--监听页面初次渲染完成\r\n\tonReady() {\r\n\t\tthis.loadmore = 'nomore'\r\n\t},\r\n\t// 页面周期函数--监听页面显示(not-nvue)\r\n\tonShow() {\r\n\r\n\t},\r\n\t// 页面周期函数--监听页面隐藏\r\n\tonHide() { },\r\n\t// 页面周期函数--监听页面卸载\r\n\tonUnload() { },\r\n\t// 页面处理函数--监听用户上拉触底\r\n\tonReachBottom() { },\r\n\t// 页面处理函数--监听用户下拉动作\r\n\tonPullDownRefresh() { },\r\n\t// 页面周期函数--监听页面返回\r\n\tonBackPress() { },\r\n\t// 页面处理函数--监听页面滚动(not-nvue)\r\n\tonPageScroll(e) { },\r\n\t// 页面处理函数--用户点击右上角分享好友\r\n\tonShareAppMessage(options) { },\r\n\t// 页面处理函数--用户点击右上角分享朋友圈\r\n\tonShareTimeline(options) { }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.activation-content {\r\n\twidth: 100%;\r\n\tmin-height: 100vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tbackground-color: #FFF;\r\n\r\n\t.u-nav-slot {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #000000;\r\n\t}\r\n\r\n\t.activation-box {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 43rpx 43rpx 0 0;\r\n\t\tposition: relative;\r\n\t\tbottom: 33rpx;\r\n\r\n\t\t.box-title {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #000000;\r\n\t\t\tmargin: 140rpx 0 70rpx;\r\n\t\t}\r\n\r\n\t\t.activation-input {\r\n\t\t\twidth: 660rpx;\r\n\t\t\theight: 103rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tborder-radius: 15rpx;\r\n\t\t\tpadding: 0rpx 40rpx;\r\n\t\t\tborder: 3rpx solid #B2B2B2;\r\n\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t&.input-focused {\r\n\t\t\t\tborder-color: #FFD101;\r\n\t\t\t\tbox-shadow: 0 0 0 2rpx rgba(255, 209, 1, 0.2);\r\n\t\t\t}\r\n\r\n\t\t\t&.input-filled {\r\n\t\t\t\tborder-color: #4CAF50;\r\n\t\t\t\tbox-shadow: 0 0 0 2rpx rgba(76, 175, 80, 0.2);\r\n\t\t\t}\r\n\r\n\t\t\t.input {\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tfont-size: 42rpx;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t}\r\n\r\n\t\t\t.input-counter {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\tmin-width: 80rpx;\r\n\t\t\t\ttext-align: right;\r\n\r\n\t\t\t\t&.counter-complete {\r\n\t\t\t\t\tcolor: #4CAF50;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.placeholder {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #B2B2B2;\r\n\t\t\tmargin: 20rpx 0 104rpx;\r\n\t\t}\r\n\r\n\t\t.button {\r\n\t\t\twidth: 356rpx;\r\n\t\t\theight: 100rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tcolor: #000000;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tbackground-color: #FFD101;\r\n\t\t\tborder-radius: 21rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.overlay-content {\r\n\t\twidth: 100%;\r\n\t\theight: 100vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t.overlay-box {\r\n\t\t\twidth: 650rpx;\r\n\t\t\tbackground-color: #FFF;\r\n\t\t\tborder-radius: 15rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 80rpx 45rpx 65rpx;\r\n\r\n\t\t\t&.overlay-free-box {\r\n\t\t\t\tpadding: 35rpx 45rpx 75rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.box-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tmargin-bottom: 80rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.box-free-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tmargin-bottom: 72rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.select-breadcrumb {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t.label {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #000000;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.value {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #F4621A;\r\n\r\n\t\t\t\t\t&:not(:last-child)::after {\r\n\t\t\t\t\t\tcontent: '-';\r\n\t\t\t\t\t\tcolor: #B2B2B2;\r\n\t\t\t\t\t\tmargin: 0 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.select-content {\r\n\t\t\t\twidth: 560rpx;\r\n\t\t\t\tmax-height: 525rpx;\r\n\t\t\t\tmargin: 14rpx 0 25rpx;\r\n\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t\t.category-list {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\tborder-radius: rpx;\r\n\r\n\t\t\t\t\t.category-li {\r\n\t\t\t\t\t\theight: 105rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\tborder: 3rpx solid #B2B2B2;\r\n\t\t\t\t\t\tbackground-color: #DEDEDE;\r\n\r\n\t\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\t\tborder-radius: 15rpx 15rpx 0 0;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tborder-radius: 0 0 15rpx 15rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.placeholder {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t}\r\n\r\n\t\t\t.box-item {\r\n\t\t\t\twidth: 585rpx;\r\n\t\t\t\theight: 210rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tbackground-color: #FFF9DC;\r\n\t\t\t\tborder-radius: 9rpx 9rpx 9rpx 9rpx;\r\n\t\t\t\tborder: 2rpx dashed #FFD101;\r\n\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.1), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.1);\r\n\t\t\t\tgap: 36rpx;\r\n\t\t\t\tmargin: 24rpx 0 54rpx;\r\n\r\n\t\t\t\t.value {\r\n\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\tcolor: #000000;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.label {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #B2B2B2;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.submit-feedback {\r\n\t\t\t\twidth: 370rpx;\r\n\t\t\t\theight: 104rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tbackground-color: #FFD101;\r\n\t\t\t\tborder-radius: 21rpx;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tmargin-top: 60rpx;\r\n\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);\r\n\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\r\n\r\n\t\t\t\t&:active {\r\n\t\t\t\t\tbackground-color: #f7ca00;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.close-icon {\r\n\t\t\twidth: 85rpx;\r\n\t\t\theight: 85rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmargin-top: 60rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tborder: 4px solid #000;\r\n\t\t\tbackground-color: #B2B2B2;\r\n\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\r\n\r\n\t\t\t&:active {\r\n\t\t\t\tbackground-color: #9f9f9f;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=43e462dc&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=43e462dc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749999203075\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}