<template>
	<view class="activation-content">
		<u-navbar :placeholder="true" bgColor="#FFD101">
			<view class="u-nav-slot" slot="left">激活</view>
		</u-navbar>
		<u-gap height="50rpx" bgColor="#FFD101" />
		<view class="activation-box">
			<text class="box-title">请输入激活码</text>
			<view class="activation-input"
				:class="{ 'input-focused': isFocused, 'input-filled': activationCode.length === 16 }">
				<input class="input" v-model="displayCode" @input="onActivationCodeInput" @focus="onInputFocus"
					@blur="onInputBlur" placeholder="请输入激活码" maxlength="19" type="text" />
				<view class="input-counter" :class="{ 'counter-complete': activationCode.length === 16 }">{{
					activationCode.length }}/16</view>
			</view>
			<text class="placeholder">请输入16位激活码</text>
			<button class="u-reset-button button" @click="showCategorySelector" style="margin-bottom: 20rpx;">选择内容</button>
			<button class="u-reset-button button" @click="onPractise(1)">激活</button>
		</view>
		<u-overlay :show="showOverlay">
			<view class="overlay-content">
				<view class="overlay-box">
					<text class="box-title">激活码：{{ displayCode }}</text>
					<text class="placeholder">将激活以下内容</text>
					<view class="box-item">
						<text class="value">与豆伴匠配套-R系列</text>
						<text class="label">有效期1年</text>
					</view>
					<button class="u-reset-button submit-feedback" @click="confirmActivation">确定激活</button>
				</view>
				<view class="close-icon" @click="closeOverlay">
					<u-icon name="close" color="#000" bold size="50rpx" />
				</view>
			</view>
		</u-overlay>
		<u-overlay :show="showFreeOverlay">
			<view class="overlay-content">
				<view class="overlay-box overlay-free-box">
					<text class="box-free-title">请选择激活内容</text>
					<view class="select-breadcrumb">
						<text class="label">已选：</text>
						<text class="value">语文</text>
					</view>
					<scroll-view class="select-content" scroll-y="true" :scroll-top="scrollTop">
						<view class="category-list">
							<view class="category-li" v-for="(item1, index1) in categoryData" :key="index1">{{ item1.name }}</view>
						</view>
					</scroll-view>
					<view class="">

					</view>
					<button class="u-reset-button submit-feedback" @click="confirmActivation">确定激活</button>
				</view>
				<view class="close-icon" @click="showFreeOverlay = false">
					<u-icon name="close" color="#000" bold size="50rpx" />
				</view>
			</view>
		</u-overlay>
		<!-- 分类选择器 -->
		<CategorySelector :visible="selectorVisible" :data="categoryData" @confirm="onCategoryConfirm"
			@close="closeCategorySelector" />
	</view>
</template>

<script>
import CategorySelector from '@/components/CategorySelector.vue'

export default {
	components: {
		CategorySelector
	},
	data: () => ({
		showOverlay: false, // 是否显示激活码确认弹窗
		showFreeOverlay: true, // 显示自由选择弹窗
		loading: true, // 加载状态
		activationCode: '', // 激活码（纯数字字母，无空格）
		displayCode: '', // 显示的激活码（带空格格式化）
		isFocused: false, // 输入框是否聚焦
		selectorVisible: false, // 分类选择器显示状态
		selectedCategory: null, // 选中的分类
		categoryData: [ // 分类数据
			{
				id: 1,
				name: '语文',
				children: [
					{
						id: 11,
						name: '常规',
						children: [
							{
								id: 111,
								name: '读写系列',
								children: [
									{ id: 1111, name: '一级目录内容' },
									{ id: 1112, name: '一级目录内容' },
									{ id: 1113, name: '一级目录内容' },
									{ id: 1114, name: '一级目录内容' },
									{ id: 1115, name: '一级目录内容' }
								]
							},
							{
								id: 112,
								name: '基础系列',
								children: [
									{ id: 1121, name: '基础练习A' },
									{ id: 1122, name: '基础练习B' }
								]
							}
						]
					},
					{
						id: 12,
						name: '专项',
						children: [
							{
								id: 121,
								name: '阅读理解',
								children: [
									{ id: 1211, name: '现代文阅读' },
									{ id: 1212, name: '古诗文阅读' }
								]
							}
						]
					}
				]
			},
			{
				id: 2,
				name: '数学',
				children: [
					{
						id: 21,
						name: '基础',
						children: [
							{
								id: 211,
								name: '计算系列',
								children: [
									{ id: 2111, name: '加减法' },
									{ id: 2112, name: '乘除法' }
								]
							}
						]
					}
				]
			},
			{
				id: 3,
				name: '英语',
				children: [
					{
						id: 31,
						name: '词汇',
						children: [
							{
								id: 311,
								name: '基础词汇',
								children: [
									{ id: 3111, name: '日常用语' },
									{ id: 3112, name: '学科词汇' }
								]
							}
						]
					}
				]
			}
		]
	}),
	filters: {},
	methods: {
		// 关闭弹窗
		closeOverlay() {
			this.showOverlay = false
		},

		// 显示分类选择器
		showCategorySelector() {
			this.selectorVisible = true;
		},

		// 关闭分类选择器
		closeCategorySelector() {
			this.selectorVisible = false;
		},

		// 分类选择确认
		onCategoryConfirm(result) {
			this.selectedCategory = result.finalSelection;
			console.log('选择的分类:', result);

			// 显示选择结果
			const path = result.selectedPath.map(item => item.name).join(' - ');
			uni.showToast({
				title: `已选择: ${path}`,
				icon: 'none',
				duration: 3000
			});

			this.closeCategorySelector();
		},
		// 激活码输入处理
		onActivationCodeInput(e) {
			let value = e.detail.value;

			// 移除所有空格和非字母数字字符
			let cleanValue = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();

			// 限制最大长度为16位
			if (cleanValue.length > 16) {
				cleanValue = cleanValue.substring(0, 16);
			}

			// 保存纯净的激活码
			this.activationCode = cleanValue;

			// 格式化显示（每4位加一个空格）
			this.displayCode = this.formatActivationCode(cleanValue);
		},

		// 格式化激活码显示
		formatActivationCode(code) {
			// 每4位添加一个空格
			return code.replace(/(.{4})/g, '$1 ').trim();
		},

		// 输入框聚焦
		onInputFocus() {
			this.isFocused = true;
		},

		// 输入框失焦
		onInputBlur() {
			this.isFocused = false;
		},

		// 验证激活码格式
		validateActivationCode() {
			if (this.activationCode.length !== 16) {
				uni.showToast({
					title: '请输入16位激活码',
					icon: 'none',
					duration: 2000
				});
				return false;
			}

			// 检查是否包含有效字符（字母和数字）
			const validPattern = /^[A-Z0-9]{16}$/;
			if (!validPattern.test(this.activationCode)) {
				uni.showToast({
					title: '激活码格式不正确',
					icon: 'none',
					duration: 2000
				});
				return false;
			}

			return true;
		},

		// 激活操作
		onPractise(e) {
			// 验证激活码
			if (!this.validateActivationCode()) {
				return;
			}
			this.showOverlay = true
		},
		// 确定激活
		confirmActivation() {
			if (!this.validateActivationCode()) {
				return;
			}

			// 模拟激活成功
			uni.showToast({
				title: '激活成功',
				icon: 'success',
				duration: 2000
			});
			this.activationCode = '' // 激活码（纯数字字母，无空格）
			this.displayCode = '' // 显示的激活码（带空格格式化）
			this.closeOverlay();
		},
	},
	// 页面周期函数--监听页面加载
	onLoad(options) {

	},
	// 页面周期函数--监听页面初次渲染完成
	onReady() {
		this.loadmore = 'nomore'
	},
	// 页面周期函数--监听页面显示(not-nvue)
	onShow() {

	},
	// 页面周期函数--监听页面隐藏
	onHide() { },
	// 页面周期函数--监听页面卸载
	onUnload() { },
	// 页面处理函数--监听用户上拉触底
	onReachBottom() { },
	// 页面处理函数--监听用户下拉动作
	onPullDownRefresh() { },
	// 页面周期函数--监听页面返回
	onBackPress() { },
	// 页面处理函数--监听页面滚动(not-nvue)
	onPageScroll(e) { },
	// 页面处理函数--用户点击右上角分享好友
	onShareAppMessage(options) { },
	// 页面处理函数--用户点击右上角分享朋友圈
	onShareTimeline(options) { }
};
</script>
<style lang="scss" scoped>
.activation-content {
	width: 100%;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #FFF;

	.u-nav-slot {
		font-size: 36rpx;
		font-weight: 600;
		color: #000000;
	}

	.activation-box {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-sizing: border-box;
		background-color: #fff;
		border-radius: 43rpx 43rpx 0 0;
		position: relative;
		bottom: 33rpx;

		.box-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #000000;
			margin: 140rpx 0 70rpx;
		}

		.activation-input {
			width: 660rpx;
			height: 103rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 15rpx;
			padding: 0rpx 40rpx;
			border: 3rpx solid #B2B2B2;
			transition: all 0.3s ease;

			&.input-focused {
				border-color: #FFD101;
				box-shadow: 0 0 0 2rpx rgba(255, 209, 1, 0.2);
			}

			&.input-filled {
				border-color: #4CAF50;
				box-shadow: 0 0 0 2rpx rgba(76, 175, 80, 0.2);
			}

			.input {
				height: 90rpx;
				width: 100%;
				font-size: 42rpx;
				color: #000000;
			}

			.input-counter {
				font-size: 28rpx;
				color: #999;
				margin-left: 10rpx;
				min-width: 80rpx;
				text-align: right;

				&.counter-complete {
					color: #4CAF50;
					font-weight: bold;
				}
			}
		}

		.placeholder {
			font-size: 30rpx;
			color: #B2B2B2;
			margin: 20rpx 0 104rpx;
		}

		.button {
			width: 356rpx;
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 36rpx;
			color: #000000;
			font-weight: 600;
			background-color: #FFD101;
			border-radius: 21rpx;
		}
	}

	.overlay-content {
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		.overlay-box {
			width: 650rpx;
			background-color: #FFF;
			border-radius: 15rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 80rpx 45rpx 65rpx;

			&.overlay-free-box {
				padding: 35rpx 45rpx 75rpx;
			}

			.box-title {
				font-size: 32rpx;
				color: #000000;
				font-weight: 600;
				margin-bottom: 80rpx;
			}

			.box-free-title {
				font-size: 32rpx;
				color: #000000;
				font-weight: 600;
				margin-bottom: 72rpx;
			}

			.select-breadcrumb {
				width: 100%;
				display: flex;
				align-items: center;

				.label {
					font-size: 30rpx;
					color: #000000;
				}

				.value {
					font-size: 30rpx;
					color: #F4621A;

					&:not(:last-child)::after {
						content: '-';
						color: #B2B2B2;
						margin: 0 10rpx;
					}
				}
			}

			.select-content {
				width: 560rpx;
				max-height: 525rpx;
				margin: 14rpx 0 25rpx;
				border-radius: 15rpx;
				box-sizing: border-box;

				.category-list {
					width: 100%;
					display: flex;
					flex-direction: column;
					box-sizing: border-box;
					border-radius: rpx;

					.category-li {
						height: 105rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						border: 3rpx solid #B2B2B2;
						background-color: #DEDEDE;

						&:first-child {
							border-radius: 15rpx 15rpx 0 0;
						}

						&:last-child {
							border-radius: 0 0 15rpx 15rpx;
						}
					}
				}
			}

			.placeholder {
				font-size: 30rpx;
				color: #000000;
			}

			.box-item {
				width: 585rpx;
				height: 210rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				background-color: #FFF9DC;
				border-radius: 9rpx 9rpx 9rpx 9rpx;
				border: 2rpx dashed #FFD101;
				box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.1), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.1);
				gap: 36rpx;
				margin: 24rpx 0 54rpx;

				.value {
					font-size: 34rpx;
					color: #000000;
				}

				.label {
					font-size: 30rpx;
					color: #B2B2B2;
				}
			}

			.submit-feedback {
				width: 370rpx;
				height: 104rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #FFD101;
				border-radius: 21rpx;
				font-size: 36rpx;
				color: #000000;
				margin-top: 60rpx;
				box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);
				transition: all 150ms cubic-bezier(.36, .66, .04, 1);

				&:active {
					background-color: #f7ca00;
				}
			}
		}

		.close-icon {
			width: 85rpx;
			height: 85rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 60rpx;
			border-radius: 50%;
			border: 4px solid #000;
			background-color: #B2B2B2;
			transition: all 150ms cubic-bezier(.36, .66, .04, 1);

			&:active {
				background-color: #9f9f9f;
			}
		}
	}
}
</style>