.flex.data-v-0d5b1156 {
  display: flex;
}
.column.data-v-0d5b1156 {
  flex-direction: column;
}
.center.data-v-0d5b1156 {
  align-items: center;
}
.space-between.data-v-0d5b1156 {
  justify-content: space-between;
}
view.data-v-0d5b1156, scroll-view.data-v-0d5b1156, swiper-item.data-v-0d5b1156 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-empty.data-v-0d5b1156 {

  display: flex;

  flex-direction: row;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.u-empty__text.data-v-0d5b1156 {

  display: flex;

  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
}
.u-slot-wrap.data-v-0d5b1156 {

  display: flex;

  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
}
