.flex.data-v-1d6b1bbc {
  display: flex;
}
.column.data-v-1d6b1bbc {
  flex-direction: column;
}
.center.data-v-1d6b1bbc {
  align-items: center;
}
.space-between.data-v-1d6b1bbc {
  justify-content: space-between;
}
.select-content.data-v-1d6b1bbc {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom, #FFF3E0 10%, #F7F7F7 50%);
}
.select-content .u-nav-slot.data-v-1d6b1bbc {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  display: flex;
  align-items: center;
}
.select-content .u-nav-slot .arrow.data-v-1d6b1bbc {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}
.select-content .header-content.data-v-1d6b1bbc {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #FFD101;
  padding-top: 15rpx;
}
.select-content .header-box.data-v-1d6b1bbc {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 22rpx 25rpx;
  background-color: #FFF3E0;
  border-radius: 43rpx 43rpx 0 0;
}
.select-content .header-box .left-item.data-v-1d6b1bbc {
  height: 48rpx;
  display: flex;
  align-items: center;
}
.select-content .header-box .left-item .icon.data-v-1d6b1bbc {
  width: 35rpx;
  height: 35rpx;
  margin-right: 16rpx;
}
.select-content .header-box .left-item .text.data-v-1d6b1bbc {
  font-size: 30rpx;
  color: #000000;
  line-height: 48rpx;
}
.select-content .header-box .right-item.data-v-1d6b1bbc {
  width: 182rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #B2B2B2;
  background-color: #DDDDDD;
  border-radius: 24rpx;
  margin: 0;
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.select-content .header-box .right-item.right-item-s.data-v-1d6b1bbc {
  background-color: #FFD101;
  color: #000000;
}
.select-content .select-list.data-v-1d6b1bbc {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 16rpx 25rpx;
  background-color: #F7F7F7;
  border-radius: 43rpx 43rpx 0 0;
  margin-bottom: 180rpx;
}
.select-content .select-list .placeholder.data-v-1d6b1bbc {
  width: 160rpx;
  height: 0rpx;
}
.select-content .select-list .select-li.data-v-1d6b1bbc {
  width: 160rpx;
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 11rpx;
  position: relative;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.select-content .select-list .select-li.select-li-s.data-v-1d6b1bbc {
  background-color: #FFD101;
}
.select-content .select-list .select-li .value.data-v-1d6b1bbc {
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
  margin-right: 20rpx;
}
.select-content .select-list .select-li .icon.data-v-1d6b1bbc {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 33rpx;
  height: 33rpx;
}
.select-content .position-bottom.data-v-1d6b1bbc {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 25rpx 20rpx;
  background-color: #FFF;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);
}
.select-content .position-bottom .button-list.data-v-1d6b1bbc {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.select-content .position-bottom .button-list .button.data-v-1d6b1bbc {
  width: 338rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #DDDDDD;
  border-radius: 29rpx;
  transition: all 100ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.select-content .position-bottom .button-list .button .icon.data-v-1d6b1bbc {
  width: 60rpx;
  height: 60rpx;
  margin-right: 15rpx;
}
.select-content .position-bottom .button-list .button .text.data-v-1d6b1bbc {
  font-size: 32rpx;
  font-weight: 500;
  color: #B2B2B2;
}
.select-content .position-bottom .button-list .button.button-s.data-v-1d6b1bbc {
  background-color: #FFD101;
}
.select-content .position-bottom .button-list .button.button-s.data-v-1d6b1bbc:active {
  background-color: #f7ca00;
}
