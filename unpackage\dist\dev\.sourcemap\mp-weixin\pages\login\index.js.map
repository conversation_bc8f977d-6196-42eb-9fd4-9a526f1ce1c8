{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/KingToyo/app_tixiaomeng/pages/login/index.vue?9b02", "webpack:///E:/KingToyo/app_tixiaomeng/pages/login/index.vue?23fb", "webpack:///E:/KingToyo/app_tixiaomeng/pages/login/index.vue?8c85", "webpack:///E:/KingToyo/app_tixiaomeng/pages/login/index.vue?807a", "uni-app:///pages/login/index.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/login/index.vue?403a", "webpack:///E:/KingToyo/app_tixiaomeng/pages/login/index.vue?80e6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "selected", "phoneNumber", "computed", "methods", "<PERSON><PERSON><PERSON><PERSON>", "uni", "url", "getrealtimephonenumber", "that", "icon", "title", "duration", "e", "wxGetPhoneNumber", "code", "setTimeout", "checkProtocol", "openClose", "delta", "watch", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onPullDownRefresh", "onReachBottom", "onBackPress"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiB9rB;EACAC;EACAC;IAAA;MACAC;MAAA;MACAC;IACA;EAAA;;EACAC;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAH;kBACAI;kBACAC;kBACAC;gBACA;gBAAA,iCACA;cAAA;gBAAA,MAEAC;kBAAA;kBAAA;gBAAA;gBACAP;gBAAA;gBAAA,OACAQ;kBAAAC;gBAAA;cAAA;gBAAA;gBAAAA;gBAAAf;gBACA;kBACA;oBACA;oBACA;oBACAM;sBACAI;sBACAC;sBACAC;oBACA;oBACAI;sBACA;sBACAP;oBACA;kBACA;oBACAH;sBACAI;sBACAC;sBACAC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAN;kBACAI;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAM;MACA;QACAX;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IACA;IACA;IACAW;MACA;MACA;MACA;QACA;QACAZ;UAAAa;QAAA;MACA;QACAb;UAAAC;QAAA;MACA;IACA;EACA;EACAa;EACA;EACAC;EACA;EACAC;IACA;IACA;IACAhB;MACAC;IACA;EACA;EACA;EACAgB;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC,uCACA;EACA;EACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAAyyC,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACA7zC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4586967a&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4586967a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4586967a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=4586967a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"login-content\">\r\n\t\t<!-- <image class=\"logo\" src=\"@/static/images/avatar.png\" />\r\n\t\t<button class=\"u-reset-button item-button\" @click=\"toLogin\">去登录</button>\r\n\t\t<view class=\"protocol-privacy\">\r\n\t\t\t<image class=\"image\" v-if=\"selected\" @tap=\"selected = false\" src=\"@/static/images/agree-icon.png\" />\r\n\t\t\t<image class=\"image\" v-if=\"!selected\" @tap=\"selected = true\" src=\"@/static/images/disagree-icon.png\" />\r\n\t\t\t<text class=\"text\">我已阅读并同意</text>\r\n\t\t\t<text class=\"text\" @tap=\"checkProtocol(1)\">《用户协议》</text>\r\n\t\t\t<text class=\"text\">和</text>\r\n\t\t\t<text class=\"text\" @tap=\"checkProtocol(2)\">《隐私政策》</text>\r\n\t\t</view> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\r\nexport default {\r\n  components: {},\r\n  data: () => ({\r\n\t\tselected: true, // 选中协议\r\n\t\tphoneNumber: '', // 手机号\r\n  }),\r\n  computed: {},\r\n  methods: {\r\n\t\t// 去登录\r\n\t\ttoLogin() {\r\n\t\t\tuni.redirectTo({\r\n\t\t\t\turl: '/pages/index/index'\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 获取手机号\r\n\t\tasync getrealtimephonenumber(e) {\r\n\t\t\tlet that = this\r\n\t\t\tif (!that.selected) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\ttitle: \"请确认并勾选下方《用户协议》和《隐私政策》\",\r\n\t\t\t\t\tduration: 1500\r\n\t\t\t\t})\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t\tif (e.detail.errMsg == 'getPhoneNumber:ok') {\r\n\t\t\t\tuni.showLoading({})\r\n\t\t\t\tlet { code, data } = await wxGetPhoneNumber({ code: e.detail.code })\r\n\t\t\t\tif (code == '00000') {\r\n\t\t\t\t\tif (data.token) {\r\n\t\t\t\t\t\tthis.$store.dispatch(\"SetToken\", data.token)\r\n\t\t\t\t\t\tthis.$store.dispatch(\"SetUserId\", data.userId)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t// 返回上一页\r\n\t\t\t\t\t\t\tthat.openClose()\r\n\t\t\t\t\t\t}, 900)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: '返回为空~',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '获取失败，请重试~'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 用户协议、隐私政策\r\n\t\tcheckProtocol(e) {\r\n\t\t\tif (e == 1) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/pages/login/agreement\"\r\n\t\t\t\t})\r\n\t\t\t} else if (e == 2) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/pages/login/privacy\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 取消返回上一页\r\n\t\topenClose() {\r\n\t\t  let pages = getCurrentPages();\r\n\t\t  let currentPages = pages.length\r\n\t\t  if (currentPages > 1) {\r\n\t\t\t\tlet beforePage = pages[pages.length -2]\r\n\t\t    uni.navigateBack({ delta: 1 })\r\n\t\t  } else {\r\n\t\t    uni.switchTab({ url: \"/pages/index/index\" });\r\n\t\t  }\r\n\t\t},\r\n\t},\r\n  watch: {},\r\n  // 页面周期函数--监听页面加载\r\n  onLoad(options) {},\r\n  // 页面周期函数--监听页面初次渲染完成\r\n  onReady() {\r\n\t\tthis.$store.dispatch(\"Logout\") // 清除vuex记录\r\n\t\tthis.$storage.clear(); // 清除所有缓存\r\n\t\tuni.redirectTo({\r\n\t\t\turl: '/pages/index/index'\r\n\t\t})\r\n\t},\r\n  // 页面周期函数--监听页面显示(not-nvue)\r\n  onShow() {},\r\n  // 页面周期函数--监听页面隐藏\r\n  onHide() {},\r\n  // 页面周期函数--监听页面卸载\r\n  onUnload() {},\r\n  // 页面处理函数--监听用户下拉动作\r\n  onPullDownRefresh() {},\r\n  // 页面处理函数--监听用户上拉触底\r\n  onReachBottom() {},\r\n\t// 页面周期函数--监听页面返回\r\n\tonBackPress() {},\r\n  // 页面处理函数--监听页面滚动(not-nvue)\r\n  /* onPageScroll(event) {}, */\r\n  // 页面处理函数--用户点击右上角分享\r\n  /* onShareAppMessage(options) {}, */\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.login-content {\r\n\twidth: 100%;\r\n\tmin-height: 100vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbox-sizing: border-box;\r\n\tbackground-color: #F3F4F6;\r\n\tpadding: 25rpx 25rpx 0rpx 25rpx;\r\n\t.logo {\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\tmargin-bottom: 200rpx;\r\n\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 255, 255, 0.1), 0rpx 6rpx 16rpx 0rpx rgba(255, 255, 255, 0.1);\r\n\t}\r\n\t.item-button {\r\n\t\twidth: 500rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #FFF;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 500;\r\n\t\tfont-family: PingFang SC;\r\n\t\tbackground-color: #2ED573;\r\n\t\ttransition: all 100ms cubic-bezier(.36,.66,.04,1);\r\n\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(46,213,115,0.2), 0rpx 6rpx 16rpx 0rpx rgba(46,213,115,0.2);\r\n\t\t&:active {\r\n\t\t\tbackground-color: #25ab5a;\r\n\t\t}\r\n\t}\r\n\t.protocol-privacy {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-top: 60rpx;\r\n\t\tmargin-bottom: 350rpx;\r\n\t\t.image {\r\n\t\t\twidth: 34rpx;\r\n\t\t\theight: 34rpx;\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t}\r\n\t\t.text {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-family: PingFang SC;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #111111;\r\n\t\t\t&:nth-child(3) {\r\n\t\t\t\tcolor: #2558B3;\r\n\t\t\t}\r\n\t\t\t&:nth-child(5) {\r\n\t\t\t\tcolor: #2558B3;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4586967a&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4586967a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749956143919\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}