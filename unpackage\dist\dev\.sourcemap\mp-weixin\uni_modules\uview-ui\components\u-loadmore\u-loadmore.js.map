{"version": 3, "sources": ["webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-loadmore/u-loadmore.vue?5bb5", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-loadmore/u-loadmore.vue?9e2a", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-loadmore/u-loadmore.vue?c596", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-loadmore/u-loadmore.vue?7d9b", "uni-app:///uni_modules/uview-ui/components/u-loadmore/u-loadmore.vue", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-loadmore/u-loadmore.vue?48b3", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-loadmore/u-loadmore.vue?a304"], "names": ["name", "mixins", "data", "dotText", "computed", "loadTextStyle", "color", "fontSize", "lineHeight", "backgroundColor", "showText", "text", "methods", "loadMore"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAA+qB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACsDnsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAxBA,eAyBA;EACAA;EACAC;EACAC;IACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA,6DACA,2DACA,oEACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpHA;AAAA;AAAA;AAAA;AAA8yC,CAAgB,2wCAAG,EAAC,C;;;;;;;;;;;ACAl0C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-loadmore/u-loadmore.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-loadmore.vue?vue&type=template&id=055cbf89&scoped=true&\"\nvar renderjs\nimport script from \"./u-loadmore.vue?vue&type=script&lang=js&\"\nexport * from \"./u-loadmore.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-loadmore.vue?vue&type=style&index=0&id=055cbf89&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"055cbf89\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-loadmore/u-loadmore.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-loadmore.vue?vue&type=template&id=055cbf89&scoped=true&\"", "var components\ntry {\n  components = {\n    uLine: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-line/u-line\" */ \"@/uni_modules/uview-ui/components/u-line/u-line.vue\"\n      )\n    },\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"@/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([\n    _vm.$u.addStyle(_vm.customStyle),\n    {\n      backgroundColor: _vm.bgColor,\n      marginBottom: _vm.$u.addUnit(_vm.marginBottom),\n      marginTop: _vm.$u.addUnit(_vm.marginTop),\n      height: _vm.$u.addUnit(_vm.height),\n    },\n  ])\n  var s1 = _vm.__get_style([_vm.loadTextStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-loadmore.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-loadmore.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t    class=\"u-loadmore\"\r\n\t    :style=\"[\r\n\t\t\t$u.addStyle(customStyle),\r\n\t\t\t{\r\n\t\t\t\tbackgroundColor: bgColor,\r\n\t\t\t\tmarginBottom: $u.addUnit(marginBottom),\r\n\t\t\t\tmarginTop: $u.addUnit(marginTop),\r\n\t\t\t\theight: $u.addUnit(height),\r\n\t\t\t},\r\n\t\t]\"\r\n\t>\r\n\t\t<u-line\r\n\t\t    length=\"140rpx\"\r\n\t\t    :color=\"lineColor\"\r\n\t\t    :hairline=\"false\"\r\n\t\t\t:dashed=\"dashed\"\r\n\t\t\tv-if=\"line\"\r\n\t\t></u-line>\r\n\t\t<!-- 加载中和没有更多的状态才显示两边的横线 -->\r\n\t\t<view\r\n\t\t    :class=\"status == 'loadmore' || status == 'nomore' ? 'u-more' : ''\"\r\n\t\t    class=\"u-loadmore__content\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t    class=\"u-loadmore__content__icon-wrap\"\r\n\t\t\t    v-if=\"status === 'loading' && icon\"\r\n\t\t\t>\r\n\t\t\t\t<u-loading-icon\r\n\t\t\t\t    :color=\"iconColor\"\r\n\t\t\t\t    :size=\"iconSize\"\r\n\t\t\t\t    :mode=\"loadingIcon\"\r\n\t\t\t\t></u-loading-icon>\r\n\t\t\t</view>\r\n\t\t\t<!-- 如果没有更多的状态下，显示内容为dot（粗点），加载特定样式 -->\r\n\t\t\t<text\r\n\t\t\t    class=\"u-line-1\"\r\n\t\t\t    :style=\"[loadTextStyle]\"\r\n\t\t\t    :class=\"[(status == 'nomore' && isDot == true) ? 'u-loadmore__content__dot-text' : 'u-loadmore__content__text']\"\r\n\t\t\t    @tap=\"loadMore\"\r\n\t\t\t>{{ showText }}</text>\r\n\t\t</view>\r\n\t\t<u-line\r\n\t\t    length=\"140rpx\"\r\n\t\t    :color=\"lineColor\"\r\n\t\t\t:hairline=\"false\"\r\n\t\t\t:dashed=\"dashed\"\r\n\t\t\tv-if=\"line\"\r\n\t\t></u-line>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\r\n\t/**\r\n\t * loadmore 加载更多\r\n\t * @description 此组件一般用于标识页面底部加载数据时的状态。\r\n\t * @tutorial https://www.uviewui.com/components/loadMore.html\r\n\t * @property {String}\t\t\tstatus\t\t\t组件状态（默认 'loadmore' ）\r\n\t * @property {String}\t\t\tbgColor\t\t\t组件背景颜色，在页面是非白色时会用到（默认 'transparent' ）\r\n\t * @property {Boolean}\t\t\ticon\t\t\t加载中时是否显示图标（默认 true ）\r\n\t * @property {String | Number}\tfontSize\t\t字体大小（默认 14 ）\r\n\t * @property {String | Number}\ticonSize\t\t图标大小（默认 17 ）\r\n\t * @property {String}\t\t\tcolor\t\t\t字体颜色（默认 '#606266' ）\r\n\t * @property {String}\t\t\tloadingIcon\t\t加载图标（默认 'circle' ）\r\n\t * @property {String}\t\t\tloadmoreText\t加载前的提示语（默认 '加载更多' ）\r\n\t * @property {String}\t\t\tloadingText\t\t加载中提示语（默认 '正在加载...' ）\r\n\t * @property {String}\t\t\tnomoreText\t\t没有更多的提示语（默认 '没有更多了' ）\r\n\t * @property {Boolean}\t\t\tisDot\t\t\t到上一个相邻元素的距离 （默认 false ）\r\n\t * @property {String}\t\t\ticonColor\t\t加载中图标的颜色 （默认 '#b7b7b7' ）\r\n\t * @property {String}\t\t\tlineColor\t\t线条颜色（默认 #E6E8EB ）\r\n\t * @property {String | Number}\tmarginTop\t\t上边距 （默认 10 ）\r\n\t * @property {String | Number}\tmarginBottom\t下边距 （默认 10 ）\r\n\t * @property {String | Number}\theight\t\t\t高度，单位px （默认 'auto' ）\r\n\t * @property {Boolean}\t\t\tline\t\t\t是否显示左边分割线  （默认 false ）\r\n\t * @property {Boolean}\t\t\tdashed\t\t// 是否虚线，true-虚线，false-实线  （默认 false ）\r\n\t * @event {Function} loadmore status为loadmore时，点击组件会发出此事件\r\n\t * @example <u-loadmore :status=\"status\" icon-type=\"iconType\" load-text=\"loadText\" />\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-loadmore\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 粗点\r\n\t\t\t\tdotText: \"●\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 加载的文字显示的样式\r\n\t\t\tloadTextStyle() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tcolor: this.color,\r\n\t\t\t\t\tfontSize: uni.$u.addUnit(this.fontSize),\r\n\t\t\t\t\tlineHeight: uni.$u.addUnit(this.fontSize),\r\n\t\t\t\t\tbackgroundColor: this.bgColor,\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 显示的提示文字\r\n\t\t\tshowText() {\r\n\t\t\t\tlet text = '';\r\n\t\t\t\tif (this.status == 'loadmore') text = this.loadmoreText\r\n\t\t\t\telse if (this.status == 'loading') text = this.loadingText\r\n\t\t\t\telse if (this.status == 'nomore' && this.isDot) text = this.dotText;\r\n\t\t\t\telse text = this.nomoreText;\r\n\t\t\t\treturn text;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tloadMore() {\r\n\t\t\t\t// 只有在“加载更多”的状态下才发送点击事件，内容不满一屏时无法触发底部上拉事件，所以需要点击来触发\r\n\t\t\t\tif (this.status == 'loadmore') this.$emit('loadmore');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-loadmore {\r\n\t\t@include flex(row);\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tflex: 1;\r\n\r\n\t\t&__content {\r\n\t\t\tmargin: 0 15px;\r\n\t\t\t@include flex(row);\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\t&__icon-wrap {\r\n\t\t\t\tmargin-right: 8px;\r\n\t\t\t}\r\n\r\n\t\t\t&__text {\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: $u-content-color;\r\n\t\t\t}\r\n\r\n\t\t\t&__dot-text {\r\n\t\t\t\tfont-size: 15px;\r\n\t\t\t\tcolor: $u-tips-color;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-loadmore.vue?vue&type=style&index=0&id=055cbf89&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-loadmore.vue?vue&type=style&index=0&id=055cbf89&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749972065269\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}