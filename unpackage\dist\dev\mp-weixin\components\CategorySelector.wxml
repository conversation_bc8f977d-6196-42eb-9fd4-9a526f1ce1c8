<block wx:if="{{visible}}"><view data-event-opts="{{[['tap',[['onOverlayClick',['$event']]]]]}}" class="category-selector-overlay data-v-536f45d8" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="category-selector data-v-536f45d8" catchtap="__e"><view class="selector-header data-v-536f45d8"><text class="header-title data-v-536f45d8">请选择激活内容</text></view><view class="breadcrumb data-v-536f45d8"><text class="breadcrumb-label data-v-536f45d8">已选：</text><view class="breadcrumb-items data-v-536f45d8"><block wx:for="{{breadcrumbItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text data-event-opts="{{[['tap',[['goToLevel',[index]]]]]}}" class="breadcrumb-item data-v-536f45d8" bindtap="__e">{{''+item.name+''}}</text></block></view></view><view class="list-container data-v-536f45d8"><scroll-view class="category-list data-v-536f45d8" scroll-y="true" scroll-top="{{scrollTop}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['selectItem',['$0',index],[[['currentLevelData','id',item.$orig.id]]]]]]]}}" class="{{['category-item','data-v-536f45d8',(selectedItems[currentLevel]&&selectedItems[currentLevel].id===item.$orig.id)?'item-selected':'']}}" bindtap="__e"><text class="item-text data-v-536f45d8">{{item.$orig.name}}</text><block wx:if="{{item.g0}}"><view class="item-arrow data-v-536f45d8"><text class="arrow-icon data-v-536f45d8">></text></view></block></view></block></scroll-view></view><view class="hint-text data-v-536f45d8"><text class="hint data-v-536f45d8">请滑动选择要激活的内容</text></view><view class="action-buttons data-v-536f45d8"><block wx:if="{{currentLevel>0}}"><button data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="btn btn-back data-v-536f45d8" bindtap="__e">返回</button></block><button data-event-opts="{{[['tap',[['nextStep',['$event']]]]]}}" class="btn btn-next data-v-536f45d8" bindtap="__e">下一步</button></view></view></view></block>