<view class="practise-content data-v-59d936fe"><u-navbar vue-id="e14ca6b6-1" placeholder="{{true}}" bgColor="#FFD101" autoBack="{{true}}" class="data-v-59d936fe" bind:__l="__l" vue-slots="{{['left']}}"><view class="u-nav-slot data-v-59d936fe" slot="left"><image class="arrow data-v-59d936fe" src="/static/images/arrow-left.png"></image><text class="label data-v-59d936fe">答题</text><text class="value data-v-59d936fe">30 / 456</text></view></u-navbar><view class="practise-box data-v-59d936fe"><view class="practise-type data-v-59d936fe"><text class="type data-v-59d936fe">{{questionData.type}}</text><text class="schedule data-v-59d936fe">{{questionData.schedule}}</text></view><text class="content data-v-59d936fe" user-select="{{true}}">{{questionData.content}}</text><view class="options-list data-v-59d936fe"><block wx:for="{{$root.l0}}" wx:for-item="option" wx:for-index="__i0__" wx:key="label"><view data-event-opts="{{[['tap',[['selectAnswer',['$0'],[[['questionData.options','label',option.$orig.label]]]]]]]}}" class="{{['options-li','data-v-59d936fe',option.m0]}}" bindtap="__e"><text class="option-label data-v-59d936fe">{{option.$orig.label}}</text><text class="option-content data-v-59d936fe">{{option.$orig.content}}</text></view></block></view><block wx:if="{{hasAnswered}}"><view class="correct-answer data-v-59d936fe"><text class="label data-v-59d936fe">正确答案：</text><text class="value data-v-59d936fe">{{questionData.correctAnswer}}</text></view></block></view><u-gap vue-id="e14ca6b6-2" height="22rpx" bgColor="#F7F7F7" class="data-v-59d936fe" bind:__l="__l"></u-gap><block wx:if="{{showAnalysis}}"><view class="answer-analysis data-v-59d936fe"><view class="analysis-title data-v-59d936fe"><text class="title data-v-59d936fe">答题解析</text><text class="icon data-v-59d936fe"></text></view><text class="analysis-content data-v-59d936fe" user-select="{{true}}">{{questionData.analysis}}</text></view></block><liu-drag-button vue-id="e14ca6b6-3" bottomPx="{{10}}" rightPx="{{5}}" data-event-opts="{{[['^clickBtn',[['onFeedback']]]]}}" bind:clickBtn="__e" class="data-v-59d936fe" bind:__l="__l" vue-slots="{{['default']}}"><view class="feedback data-v-59d936fe"><image class="icon data-v-59d936fe" src="/static/images/feedback.png"></image><text class="text data-v-59d936fe">反馈</text></view></liu-drag-button><u-gap vue-id="e14ca6b6-4" height="240rpx" bgColor="transparent" class="data-v-59d936fe" bind:__l="__l"></u-gap><u-safe-bottom vue-id="e14ca6b6-5" class="data-v-59d936fe" bind:__l="__l"></u-safe-bottom><view class="position-bottom data-v-59d936fe"><view class="button-list data-v-59d936fe"><button data-event-opts="{{[['tap',[['onPractise',[1]]]]]}}" class="{{['u-reset-button','button','data-v-59d936fe',(isNextStep)?'button-s':'']}}" bindtap="__e"><text class="text data-v-59d936fe">上一题</text></button><button data-event-opts="{{[['tap',[['onPractise',[2]]]]]}}" class="u-reset-button button button-s data-v-59d936fe" bindtap="__e"><image class="icon data-v-59d936fe" src="/static/images/collect.png"></image><text class="text data-v-59d936fe">收藏</text></button><button data-event-opts="{{[['tap',[['onPractise',[2]]]]]}}" class="u-reset-button button button-s next-step data-v-59d936fe" bindtap="__e"><text class="text data-v-59d936fe">下一题</text><text class="value data-v-59d936fe">2/456</text></button></view><u-safe-bottom vue-id="e14ca6b6-6" class="data-v-59d936fe" bind:__l="__l"></u-safe-bottom></view></view>