<view class="practise-content data-v-59d936fe"><u-navbar vue-id="e14ca6b6-1" placeholder="{{true}}" bgColor="#FFD101" autoBack="{{true}}" class="data-v-59d936fe" bind:__l="__l" vue-slots="{{['left']}}"><view class="u-nav-slot data-v-59d936fe" slot="left"><image class="arrow data-v-59d936fe" src="/static/images/arrow-left.png"></image><text class="label data-v-59d936fe">答题</text><text class="value data-v-59d936fe">{{progressText}}</text></view></u-navbar><view class="practise-box data-v-59d936fe"><view class="practise-type data-v-59d936fe"><text class="type data-v-59d936fe">{{currentQuestion.type}}</text><text class="schedule data-v-59d936fe">{{currentQuestion.schedule}}</text></view><text class="content data-v-59d936fe" user-select="{{true}}">{{currentQuestion.content}}</text><view class="options-list data-v-59d936fe"><block wx:for="{{$root.l0}}" wx:for-item="option" wx:for-index="__i0__" wx:key="label"><view data-event-opts="{{[['tap',[['selectAnswer',['$0'],[[['currentQuestion.options','label',option.$orig.label]]]]]]]}}" class="{{['options-li','data-v-59d936fe',option.m0]}}" bindtap="__e"><text class="option-label data-v-59d936fe">{{option.$orig.label}}</text><text class="option-content data-v-59d936fe">{{option.$orig.content}}</text></view></block></view><block wx:if="{{currentQuestion.hasAnswered}}"><view class="correct-answer data-v-59d936fe"><text class="label data-v-59d936fe">正确答案：</text><text class="value data-v-59d936fe">{{currentQuestion.correctAnswer}}</text></view></block></view><u-gap vue-id="e14ca6b6-2" height="22rpx" bgColor="#F7F7F7" class="data-v-59d936fe" bind:__l="__l"></u-gap><block wx:if="{{currentQuestion.showAnalysis}}"><view class="answer-analysis data-v-59d936fe"><view class="analysis-title data-v-59d936fe"><text class="title data-v-59d936fe">答题解析</text><text class="icon data-v-59d936fe"></text></view><text class="analysis-content data-v-59d936fe" user-select="{{true}}">{{currentQuestion.analysis}}</text></view></block><block wx:if="{{!showOverlay}}"><liu-drag-button vue-id="e14ca6b6-3" bottomPx="{{10}}" rightPx="{{5}}" data-event-opts="{{[['^clickBtn',[['onFeedback']]]]}}" bind:clickBtn="__e" class="data-v-59d936fe" bind:__l="__l" vue-slots="{{['default']}}"><view class="feedback data-v-59d936fe"><image class="icon data-v-59d936fe" src="/static/images/feedback.png"></image><text class="text data-v-59d936fe">反馈</text></view></liu-drag-button></block><u-gap vue-id="e14ca6b6-4" height="240rpx" bgColor="transparent" class="data-v-59d936fe" bind:__l="__l"></u-gap><u-safe-bottom vue-id="e14ca6b6-5" class="data-v-59d936fe" bind:__l="__l"></u-safe-bottom><view class="position-bottom data-v-59d936fe"><view class="button-list data-v-59d936fe"><button data-event-opts="{{[['tap',[['onPractise',[1]]]]]}}" class="{{['u-reset-button','button','data-v-59d936fe',(hasPrevious)?'button-s':'']}}" bindtap="__e"><text class="text data-v-59d936fe">上一题</text></button><button data-event-opts="{{[['tap',[['onPractise',[2]]]]]}}" class="u-reset-button button button-s data-v-59d936fe" bindtap="__e"><block wx:if="{{!currentQuestion.isCollected}}"><image class="icon data-v-59d936fe" src="/static/images/collect.png"></image></block><text class="text data-v-59d936fe">{{currentQuestion.isCollected?'已收藏':'收藏'}}</text></button><block wx:if="{{hasNext}}"><button data-event-opts="{{[['tap',[['onPractise',[3]]]]]}}" class="u-reset-button button button-s next-step data-v-59d936fe" bindtap="__e"><text class="text data-v-59d936fe">下一题</text><text class="value data-v-59d936fe">{{nextProgressText}}</text></button></block><block wx:else><button data-event-opts="{{[['tap',[['onPractise',[4]]]]]}}" class="u-reset-button button button-s next-step data-v-59d936fe" bindtap="__e"><text class="text data-v-59d936fe">提交</text></button></block></view><u-safe-bottom vue-id="e14ca6b6-6" class="data-v-59d936fe" bind:__l="__l"></u-safe-bottom></view><u-overlay vue-id="e14ca6b6-7" show="{{showOverlay}}" class="data-v-59d936fe" bind:__l="__l" vue-slots="{{['default']}}"><view class="feedback-overlay data-v-59d936fe"><view class="feedback-box data-v-59d936fe"><text class="box-title data-v-59d936fe">请描述您需要反馈的内容</text><view class="textarea-item data-v-59d936fe"><u-textarea bind:input="__e" vue-id="{{('e14ca6b6-8')+','+('e14ca6b6-7')}}" height="470rpx" placeholder="请输入反馈内容" maxlength="{{500}}" count="{{true}}" value="{{feedbackValue}}" data-event-opts="{{[['^input',[['__set_model',['','feedbackValue','$event',[]]]]]]}}" class="data-v-59d936fe" bind:__l="__l"></u-textarea></view><button data-event-opts="{{[['tap',[['submitFeedback',['$event']]]]]}}" class="u-reset-button submit-feedback data-v-59d936fe" bindtap="__e">提交反馈</button></view><view data-event-opts="{{[['tap',[['closeOverlay',['$event']]]]]}}" class="close-icon data-v-59d936fe" bindtap="__e"><u-icon vue-id="{{('e14ca6b6-9')+','+('e14ca6b6-7')}}" name="close" color="#000" bold="{{true}}" size="50rpx" class="data-v-59d936fe" bind:__l="__l"></u-icon></view></view></u-overlay></view>