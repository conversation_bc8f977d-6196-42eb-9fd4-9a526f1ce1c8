<view class="practise-content data-v-59d936fe"><u-navbar vue-id="e14ca6b6-1" placeholder="{{true}}" bgColor="#FFD101" autoBack="{{true}}" class="data-v-59d936fe" bind:__l="__l" vue-slots="{{['left']}}"><view class="u-nav-slot data-v-59d936fe" slot="left"><image class="arrow data-v-59d936fe" src="/static/images/arrow-left.png"></image><text class="label data-v-59d936fe">答题</text><text class="value data-v-59d936fe">30 / 456</text></view></u-navbar><view class="practise-box data-v-59d936fe"><view class="practise-type data-v-59d936fe"><text class="type data-v-59d936fe">单选题</text><text class="schedule data-v-59d936fe">1-1</text></view><text class="content data-v-59d936fe" user-select="{{true}}">1、写一篇描写课间操场景的作文，运用 “七种武器”，以下哪项能使作文内容更丰富呢？（ ）</text><view class="options-list data-v-59d936fe"><view class="options-li data-v-59d936fe"><text class="option-label data-v-59d936fe">A</text><text class="option-content data-v-59d936fe">只写同学们在做课间操的动作</text></view><view class="options-li data-v-59d936fe"><text class="option-label data-v-59d936fe">B</text><text class="option-content data-v-59d936fe">从看到同学们整齐的队列、听到广播里的音乐声、闻到操场边树木的气息、摸到做操用的器材等多方面去写，同时写自己充满活力的心情</text></view><view class="options-li data-v-59d936fe"><text class="option-label data-v-59d936fe">C</text><text class="option-content data-v-59d936fe">只写自己想象中下次课间操想怎么改进</text></view><view class="options-li data-v-59d936fe"><text class="option-label data-v-59d936fe">D</text><text class="option-content data-v-59d936fe">只写看到的几个同学打闹的样子</text></view></view><view class="correct-answer data-v-59d936fe"><text class="label data-v-59d936fe">正确答案：</text><text class="value data-v-59d936fe">A</text></view></view><u-gap vue-id="e14ca6b6-2" height="22rpx" bgColor="#F7F7F7" class="data-v-59d936fe" bind:__l="__l"></u-gap><view class="answer-analysis data-v-59d936fe"><view class="analysis-title data-v-59d936fe"><text class="title data-v-59d936fe">答题解析</text><text class="icon data-v-59d936fe"></text></view><text class="analysis-content data-v-59d936fe" user-select="{{true}}">原文：“他这么想着，扇子摇得更勤了。扇子常常碰在身体上，发出啪啪的声音。他不会叫喊，这是唯一的警告主人的法子了。</text></view><liu-drag-button vue-id="e14ca6b6-3" bottomPx="{{10}}" rightPx="{{5}}" data-event-opts="{{[['^clickBtn',[['onFeedback']]]]}}" bind:clickBtn="__e" class="data-v-59d936fe" bind:__l="__l" vue-slots="{{['default']}}"><view class="feedback data-v-59d936fe"><image class="icon data-v-59d936fe" src="/static/images/feedback.png"></image><text class="text data-v-59d936fe">反馈</text></view></liu-drag-button><u-gap vue-id="e14ca6b6-4" height="240rpx" bgColor="transparent" class="data-v-59d936fe" bind:__l="__l"></u-gap><u-safe-bottom vue-id="e14ca6b6-5" class="data-v-59d936fe" bind:__l="__l"></u-safe-bottom><view class="position-bottom data-v-59d936fe"><view class="button-list data-v-59d936fe"><button data-event-opts="{{[['tap',[['onPractise',[1]]]]]}}" class="{{['u-reset-button','button','data-v-59d936fe',(isNextStep)?'button-s':'']}}" bindtap="__e"><text class="text data-v-59d936fe">上一题</text></button><button data-event-opts="{{[['tap',[['onPractise',[2]]]]]}}" class="u-reset-button button button-s data-v-59d936fe" bindtap="__e"><image class="icon data-v-59d936fe" src="/static/images/collect.png"></image><text class="text data-v-59d936fe">收藏</text></button><button data-event-opts="{{[['tap',[['onPractise',[2]]]]]}}" class="u-reset-button button button-s next-step data-v-59d936fe" bindtap="__e"><text class="text data-v-59d936fe">下一题</text><text class="value data-v-59d936fe">2/456</text></button></view><u-safe-bottom vue-id="e14ca6b6-6" class="data-v-59d936fe" bind:__l="__l"></u-safe-bottom></view></view>