<template>
	<view class="answer-content">
		<u-navbar :placeholder="true" bgColor="#FFD101" :autoBack="true">
			<view class="u-nav-slot" slot="left"><image class="arrow" src="@/static/images/arrow-left.png" /> L系列</view>
		</u-navbar>
		<view class="header-box"></view>
		<view class="header-image">
			<u-image width="100%" height="215rpx" radius="43rpx" src="@/static/images/banner.png" />
		</view>
		<view class="answer-title">
			<text class="value">L系列 - L1</text>
			<text class="all-answer">共2105题</text>
		</view>
		<view class="answer-list">
			<view class="answer-li" @click="onFunction(1)">
				<image class="icon" src="@/static/images/answer-icon-1.png" />
				<view class="title-schedule">
					<text class="title">错题本</text>
					<view class="schedule">
						<text class="label">共55题</text>
					</view>
				</view>
			</view>
			<view class="answer-li" @click="onFunction(2)">
				<image class="icon" src="@/static/images/answer-icon-2.png" />
				<view class="title-schedule">
					<text class="title">我的收藏</text>
				</view>
			</view>
			<view class="answer-li" @click="onFunction(3)">
				<image class="icon" src="@/static/images/answer-icon-3.png" />
				<view class="title-schedule">
					<text class="title">顺序练习</text>
					<view class="schedule">
						<text class="value">455</text>
						<text class="label margin">/</text>
						<text class="label">2359</text>
					</view>
				</view>
			</view>
			<view class="answer-li" @click="onFunction(4)">
				<image class="icon" src="@/static/images/answer-icon-4.png" />
				<view class="title-schedule">
					<text class="title">随机练习</text>
				</view>
			</view>
			<view class="answer-li" @click="onFunction(5)">
				<image class="icon" src="@/static/images/answer-icon-5.png" />
				<view class="title-schedule">
					<text class="title">自选练习</text>
				</view>
			</view>
			<view class="answer-li" @click="onFunction(6)">
				<image class="icon" src="@/static/images/answer-icon-5.png" />
				<view class="title-schedule">
					<text class="title">购买纸质版</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		components: {},
		data: () => ({
			
		}),
		filters: {},
		methods: {
			// 功能跳转
			onFunction(e) {
				switch (e) {
					case 1:
						// 错题本
						uni.navigateTo({
							url: '/pages/index/wrong'
						});
						break;
					case 2:
						// 我的收藏
						uni.navigateTo({
							url: '/pages/mine/myCollection'
						});
						break;
					case 3:
						// 顺序练习
						uni.navigateTo({
							url: '/pages/index/sequencePractice'
						});
						break;
					case 4:
						// 随机练习
						uni.navigateTo({
							url: '/pages/index/randomPractice'
						});
						break;
					case 5:
						// 自选练习
						uni.navigateTo({
							url: '/pages/index/customPractice'
						});
						break;
					case 6:
						// 购买纸质版
						uni.navigateTo({
							url: '/pages/index/purchasePaperVersion'
						});
						break;
				}
			},
			// 跳转至隐私协议页面
			openPrivacyContract() {
				uni.openPrivacyContract()
			},
			
		},
		// 页面周期函数--监听页面加载
		onLoad(options) {
			
		},
		// 页面周期函数--监听页面初次渲染完成
		onReady() {},
		// 页面周期函数--监听页面显示(not-nvue)
		onShow() {
			
		},
		// 页面周期函数--监听页面隐藏
		onHide() {},
		// 页面周期函数--监听页面卸载
		onUnload() {},
		// 页面处理函数--监听用户上拉触底
		onReachBottom() {},
		// 页面处理函数--监听用户下拉动作
		onPullDownRefresh() {},
		// 页面周期函数--监听页面返回
		onBackPress() {},
		// 页面处理函数--监听页面滚动(not-nvue)
		onPageScroll(e) {},
		// 页面处理函数--用户点击右上角分享好友
		onShareAppMessage(options) {},
		// 页面处理函数--用户点击右上角分享朋友圈
		onShareTimeline(options) {}
	};
</script>
<style lang="scss" scoped>
	.answer-content {
		width: 100%;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #F7F7F7;
		.u-nav-slot {
			font-size: 36rpx;
			font-weight: 600;
			color: #000000;
			display: flex;
			align-items: center;
			.arrow {
				width: 30rpx;
				height: 30rpx;
				margin-right: 10rpx;
			}
		}
		.header-box {
			width: 100%;
			height: 50rpx;
			background-color: #FFD101;
		}
		.header-image {
			width: 100%;
			height: 215rpx;
			border-radius: 43rpx;
			position: relative;
			bottom: 33rpx;
			background-color: #F7F7F7;
		}
		.answer-title {
			width: 100%;
			padding: 0 25rpx;
			display: flex;
			align-items: center;
			margin-bottom: 32rpx;
			.value {
				font-size: 30rpx;
				font-weight: 600;
				color: #000000;
			}
			.all-answer {
				font-size: 24rpx;
				font-weight: 400;
				color: #464646;
				margin-left: 10rpx;
			}
		}
		.answer-list {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-wrap: wrap;
			padding: 0 25rpx;
			.answer-li {
				width: 338rpx;
				height: 153rpx;
				display: flex;
				align-items: center;
				background-color: #FFFFFF;
				border-radius: 29rpx;
				padding-left: 35rpx;
				margin-bottom: 13rpx;
				transition: all 100ms cubic-bezier(.36, .66, .04, 1);
				box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);
				&:active {
					background-color: #fcfcfc;
				}
				.icon {
					width: 98rpx;
					height: 98rpx;
					margin-right: 16rpx;
				}
				.title-schedule {
					height: 98rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					.title {
						font-size: 32rpx;
						color: #464646;
					}
					.schedule {
						display: flex;
						align-items: center;
						margin-top: 17rpx;
						.label {
							font-size: 30rpx;
							color: #B2B2B2;
							.margin {
								margin: 0 5rpx;
							}
						}
						.value {
							font-size: 30rpx;
							color: #F4621A;
						}
					}
				}
			}
		}
	}
</style>