<view class="data-v-5860b122"><movable-area class="movable-area data-v-5860b122" scale-area="{{false}}"><movable-view class="{{['movable-view','data-v-5860b122',!isRemove?'animation-info':'']}}" style="pointer-events:auto;" direction="all" inertia="true" x="{{x}}" y="{{y}}" disabled="{{disabled}}" out-of-bounds="{{true}}" damping="{{200}}" friction="{{100}}" data-event-opts="{{[['tap',[['clickBtn',['$event']]]],['touchstart',[['touchstart',['$event']]]],['touchend',[['touchend',['$event']]]],['change',[['onChange',['$event']]]]]}}" bindtap="__e" bindtouchstart="__e" bindtouchend="__e" bindchange="__e"><slot></slot></movable-view></movable-area></view>