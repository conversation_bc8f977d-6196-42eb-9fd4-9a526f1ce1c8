<view class="wrong-content data-v-30d9a3f2"><u-navbar vue-id="0ad313df-1" placeholder="{{true}}" bgColor="#FFD101" class="data-v-30d9a3f2" bind:__l="__l" vue-slots="{{['left']}}"><view class="u-nav-slot data-v-30d9a3f2" slot="left">全部错题本</view></u-navbar><u-gap vue-id="0ad313df-2" height="50rpx" bgColor="#FFD101" class="data-v-30d9a3f2" bind:__l="__l"></u-gap><view class="wrong-list data-v-30d9a3f2"><text class="wrong-title data-v-30d9a3f2">{{"共"+$root.g0+"题"}}</text><block wx:for="{{wrongList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="wrong-li data-v-30d9a3f2"><text class="content u-line-2 data-v-30d9a3f2">{{item.content}}</text></view></block><block wx:if="{{$root.g1>0}}"><u-loadmore vue-id="0ad313df-3" line="{{true}}" status="{{loadmore}}" class="data-v-30d9a3f2" bind:__l="__l"></u-loadmore></block></view><u-empty vue-id="0ad313df-4" show="{{isData}}" text="暂无错题本" marginTop="10rpx" class="data-v-30d9a3f2" bind:__l="__l"></u-empty><u-safe-bottom vue-id="0ad313df-5" class="data-v-30d9a3f2" bind:__l="__l"></u-safe-bottom><view class="position-bottom data-v-30d9a3f2"><view class="button-list data-v-30d9a3f2"><button data-event-opts="{{[['tap',[['onPractise',[1]]]]]}}" class="{{['u-reset-button','button','data-v-30d9a3f2',($root.g2>0)?'button-s':'']}}" bindtap="__e"><text class="text data-v-30d9a3f2">清空</text></button><button data-event-opts="{{[['tap',[['onPractise',[2]]]]]}}" class="{{['u-reset-button','button','data-v-30d9a3f2',($root.g3>0)?'button-s':'']}}" bindtap="__e"><text class="text data-v-30d9a3f2">练习</text></button></view></view></view>