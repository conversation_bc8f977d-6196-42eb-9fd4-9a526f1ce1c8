<template>
	<view class="practise-content">
		<u-navbar :placeholder="true" bgColor="#FFD101" :autoBack="true">
			<view class="u-nav-slot" slot="left">
				<image class="arrow" src="@/static/images/arrow-left.png" />
				<text class="label">答题</text>
				<text class="value">30 / 456</text>
			</view>
		</u-navbar>
		<view class="practise-box">
			<view class="practise-type">
				<text class="type">{{ questionData.type }}</text>
				<text class="schedule">{{ questionData.schedule }}</text>
			</view>
			<text class="content" :user-select="true">{{ questionData.content }}</text>
			<view class="options-list">
				<view class="options-li" :class="[getOptionClass(option)]" v-for="option in questionData.options"
					:key="option.label" @click="selectAnswer(option)">
					<text class="option-label">{{ option.label }}</text>
					<text class="option-content">{{ option.content }}</text>
				</view>
			</view>
			<view class="correct-answer" v-if="hasAnswered">
				<text class="label">正确答案：</text>
				<text class="value">{{ questionData.correctAnswer }}</text>
			</view>
		</view>
		<u-gap height="22rpx" bgColor="#F7F7F7" />
		<view class="answer-analysis" v-if="showAnalysis">
			<view class="analysis-title">
				<text class="title">答题解析</text>
				<text class="icon"></text>
			</view>
			<text class="analysis-content" :user-select="true">{{ questionData.analysis }}</text>
		</view>
		<liu-drag-button @clickBtn="onFeedback" :bottomPx="10" :rightPx="5">
			<view class="feedback">
				<image class="icon" src="@/static/images/feedback.png" />
				<text class="text">反馈</text>
			</view>
		</liu-drag-button>
		<u-gap height="240rpx" bgColor="transparent" />
		<u-safe-bottom />
		<view class="position-bottom">
			<view class="button-list">
				<button class="u-reset-button button" :class="{ 'button-s': isNextStep }" @click="onPractise(1)">
					<text class="text">上一题</text>
				</button>
				<button class="u-reset-button button button-s" @click="onPractise(2)">
					<image class="icon" src="@/static/images/collect.png" />
					<text class="text">收藏</text>
				</button>
				<button class="u-reset-button button button-s next-step" @click="onPractise(2)">
					<text class="text">下一题</text>
					<text class="value">2/456</text>
				</button>
			</view>
			<u-safe-bottom />
		</view>
	</view>
</template>

<script>
export default {
	components: {},
	data: () => ({
		// 题目数据
		questionData: {
			id: 1,
			type: '单选题',
			schedule: '1-1',
			content: '1、写一篇描写课间操场景的作文，运用 "七种武器"，以下哪项能使作文内容更丰富呢？（ ）',
			options: [
				{ label: 'A', content: '只写同学们在做课间操的动作' },
				{ label: 'B', content: '从看到同学们整齐的队列、听到广播里的音乐声、闻到操场边树木的气息、摸到做操用的器材等多方面去写，同时写自己充满活力的心情' },
				{ label: 'C', content: '只写自己想象中下次课间操想怎么改进' },
				{ label: 'D', content: '只写看到的几个同学打闹的样子' }
			],
			correctAnswer: 'B', // 正确答案
			analysis: '写作文时运用"七种武器"（视觉、听觉、嗅觉、触觉、味觉、心理感受、想象）能让文章更加生动丰富。选项B正确地体现了多感官描写的特点，通过看、听、闻、摸等多个角度来描写课间操场景，同时加入心理感受，这样的写作方式能使文章内容更加丰富立体。'
		},
		// 用户选择的答案
		selectedAnswer: '',
		// 是否已经答题
		hasAnswered: false,
		// 是否显示解析
		showAnalysis: false,
		// 下一步按钮状态
		isNextStep: true
	}),
	methods: {
		// 选择答案
		selectAnswer(option) {
			if (this.hasAnswered) return; // 已经答题则不能再选择

			this.selectedAnswer = option.label;
			this.hasAnswered = true;
			this.showAnalysis = true;
		},

		// 判断选项样式类
		getOptionClass(option) {
			if (!this.hasAnswered) return '';

			if (option.label === this.questionData.correctAnswer) {
				// 正确答案显示绿色
				return 'option-correct';
			} else if (option.label === this.selectedAnswer) {
				// 用户选择的错误答案显示红色
				return 'option-wrong';
			}
			return '';
		},

		// 重置答题状态（用于下一题）
		resetAnswer() {
			this.selectedAnswer = '';
			this.hasAnswered = false;
			this.showAnalysis = false;
		},

		// 反馈功能
		onFeedback() {
			uni.showToast({
				title: '反馈功能',
				icon: 'none'
			});
		},

		// 练习功能（上一题/下一题/收藏）
		onPractise(type) {
			switch (type) {
				case 1:
					// 上一题
					uni.showToast({
						title: '上一题',
						icon: 'none'
					});
					break;
				case 2:
					// 收藏/下一题
					uni.showToast({
						title: '功能开发中',
						icon: 'none'
					});
					break;
			}
		},

		// 功能跳转
		onFunction(e) {
			switch (e) {
				case 1:
					// 错题本
					uni.navigateTo({
						url: '/pages/index/wrongBook'
					});
					break;
				case 2:
					// 我的收藏
					uni.navigateTo({
						url: '/pages/index/myCollection'
					});
					break;
				case 3:
					// 顺序练习
					uni.navigateTo({
						url: '/pages/index/sequencePractice'
					});
					break;
				case 4:
					// 随机练习
					uni.navigateTo({
						url: '/pages/index/randomPractice'
					});
					break;
				case 5:
					// 自选练习
					uni.navigateTo({
						url: '/pages/index/customPractice'
					});
					break;
				case 6:
					// 购买纸质版
					uni.navigateTo({
						url: '/pages/index/purchasePaperVersion'
					});
					break;
			}
		},
		// 跳转至隐私协议页面
		openPrivacyContract() {
			uni.openPrivacyContract()
		}
	},
	// 页面周期函数--监听页面加载
	onLoad(options) {

	},
	// 页面周期函数--监听页面初次渲染完成
	onReady() { },
	// 页面周期函数--监听页面显示(not-nvue)
	onShow() {

	},
	// 页面周期函数--监听页面隐藏
	onHide() { },
	// 页面周期函数--监听页面卸载
	onUnload() { },
	// 页面处理函数--监听用户上拉触底
	onReachBottom() { },
	// 页面处理函数--监听用户下拉动作
	onPullDownRefresh() { },
	// 页面周期函数--监听页面返回
	onBackPress() { },
	// 页面处理函数--监听页面滚动(not-nvue)
	onPageScroll(e) { },
	// 页面处理函数--用户点击右上角分享好友
	onShareAppMessage(options) { },
	// 页面处理函数--用户点击右上角分享朋友圈
	onShareTimeline(options) { }
};
</script>
<style lang="scss" scoped>
.practise-content {
	width: 100%;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-image: linear-gradient(to bottom, #FFD101 10%, #FFF 50%);

	.u-nav-slot {
		display: flex;
		align-items: center;

		.arrow {
			width: 30rpx;
			height: 30rpx;
		}

		.label {
			font-size: 36rpx;
			font-weight: 600;
			color: #000000;
			margin: 0 15rpx 0 5rpx;
		}

		.value {
			font-size: 30rpx;
			font-weight: 500;
			color: #000000;
		}
	}

	.practise-box {
		width: 100%;
		background-color: #FFFFFF;
		border-radius: 43rpx 43rpx 0 0;
		margin-top: 15rpx;
		display: flex;
		flex-direction: column;
		padding: 40rpx 25rpx;

		.practise-type {
			width: 100%;
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;

			.type {
				height: 58rpx;
				font-size: 30rpx;
				color: #000000;
				line-height: 58rpx;
				padding: 0 17rpx;
				background-color: #FFF8D8;
				border-radius: 7rpx;
				border: 2rpx solid #FFE674;
				margin-right: 25rpx;
			}

			.schedule {
				font-size: 30rpx;
				color: #464646;
			}
		}

		.content {
			width: 100%;
			font-size: 36rpx;
			font-weight: 600;
			color: #000000;
			line-height: 52rpx;
			margin-bottom: 30rpx;
		}

		.options-list {
			width: 100%;
			display: flex;
			flex-direction: column;

			.options-li {
				width: 100%;
				display: flex;
				flex-wrap: wrap;
				padding: 25rpx 0;
				border-radius: 20rpx;
				transition: all 150ms cubic-bezier(.36, .66, .04, 1);

				&:active {
					.option-content {
						text-decoration: underline wavy #c0c4cc;
					}

					background-color: #f9f9f9;
				}

				&.option-correct {
					.option-label {
						background-color: #65BC17;
						border-color: #65BC17;
						color: #FFFFFF;
					}

					.option-content {
						color: #55B300;
					}
				}

				&.option-wrong {

					.option-label {
						background-color: #F4621A;
						border-color: #F4621A;
						color: #FFFFFF;
					}

					// .option-content {
					// 	color: #F44336;
					// }
				}

				.option-label {
					width: 50rpx;
					height: 50rpx;
					font-size: 36rpx;
					line-height: 50rpx;
					text-align: center;
					box-sizing: border-box;
					color: #000000;
					border-radius: 46rpx;
					margin-right: 20rpx;
					background-color: #FFF;
					border: 2rpx solid #B2B2B2;
					transition: all 150ms cubic-bezier(.36, .66, .04, 1);
				}

				.option-content {
					width: 630rpx;
					font-size: 36rpx;
					color: #464646;
					line-height: 50rpx;
					transition: all 150ms cubic-bezier(.36, .66, .04, 1);
				}
			}
		}

		.correct-answer {
			width: 100%;
			height: 86rpx;
			background-color: #E9F8D1;
			border-radius: 10rpx;
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			margin: 40rpx 0;

			.label {
				font-size: 36rpx;
				line-height: 86rpx;
				color: #459101;
			}

			.value {
				font-size: 36rpx;
				line-height: 40rpx;
				font-weight: bold;
				color: #459101;
			}
		}
	}

	.answer-analysis {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 20rpx 25rpx;

		.analysis-title {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 35rpx;

			.title {
				font-size: 30rpx;
				font-weight: 600;
				color: #000000;
				z-index: 10;
			}

			.icon {
				width: 126rpx;
				height: 15rpx;
				background-image: linear-gradient(to right, #FFD101 0%, #FFFFFF 100%);
				border-radius: 8rpx;
				position: relative;
				bottom: 10rpx;
			}
		}

		.analysis-content {
			font-size: 36rpx;
			color: #000000;
			line-height: 52rpx;
		}
	}

	.feedback {
		position: fixed;
		left: 25rpx;
		bottom: 240rpx;
		width: 68rpx;
		display: flex;
		align-items: center;
		flex-direction: column;
		transition: all 100ms cubic-bezier(.36, .66, .04, 1);

		&:active {
			transform: scale(0.99);
		}

		.icon {
			width: 68rpx;
			height: 62rpx;
		}

		.text {
			font-size: 24rpx;
			color: #464646;
		}
	}

	.position-bottom {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 25rpx 20rpx;
		background-color: #FFF;
		box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);
		border-top: 1rpx solid #e4e7ed;

		.button-list {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.button {
				width: 186rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #DDDDDD;
				border-radius: 21rpx;
				transition: all 150ms cubic-bezier(.36, .66, .04, 1);
				margin: 0;

				.icon {
					width: 50rpx;
					height: 50rpx;
					margin-right: 10rpx;
				}

				.text {
					font-size: 32rpx;
					font-weight: 500;
					color: #B2B2B2;
				}

				.value {
					font-size: 30rpx;
					font-weight: 500;
					color: #464646;
				}

				&.button-s {
					background-color: #FFD101;
					box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);

					.text {
						color: #000;
					}

					&:active {
						background-color: #f7ca00;
					}
				}

				&.next-step {
					min-width: 272rpx;
					padding: 0 3rpx;
				}
			}
		}
	}
}
</style>
