<template>
	<view class="practise-content">
		<u-navbar :placeholder="true" bgColor="#FFD101" :autoBack="true">
			<view class="u-nav-slot" slot="left">
				<image class="arrow" src="@/static/images/arrow-left.png" />
				<text class="label">答题</text>
				<text class="value">{{ progressText }}</text>
			</view>
		</u-navbar>
		<view class="practise-box">
			<view class="practise-type">
				<text class="type">{{ currentQuestion.type }}</text>
				<text class="schedule">{{ currentQuestion.schedule }}</text>
			</view>
			<text class="content" :user-select="true">{{ currentQuestion.content }}</text>
			<view class="options-list">
				<view class="options-li" :class="[getOptionClass(option)]" v-for="option in currentQuestion.options"
					:key="option.label" @click="selectAnswer(option)">
					<text class="option-label">{{ option.label }}</text>
					<text class="option-content">{{ option.content }}</text>
				</view>
			</view>
			<view class="correct-answer" v-if="currentQuestion.hasAnswered">
				<text class="label">正确答案：</text>
				<text class="value">{{ currentQuestion.correctAnswer }}</text>
			</view>
		</view>
		<u-gap height="22rpx" bgColor="#F7F7F7" />
		<view class="answer-analysis" v-if="currentQuestion.showAnalysis">
			<view class="analysis-title">
				<text class="title">答题解析</text>
				<text class="icon"></text>
			</view>
			<text class="analysis-content" :user-select="true">{{ currentQuestion.analysis }}</text>
		</view>
		<liu-drag-button @clickBtn="onFeedback" :bottomPx="10" :rightPx="5">
			<view class="feedback" @longpress="viewAnswerRecord">
				<image class="icon" src="@/static/images/feedback.png" />
				<text class="text">反馈</text>
			</view>
		</liu-drag-button>
		<u-gap height="240rpx" bgColor="transparent" />
		<u-safe-bottom />
		<view class="position-bottom">
			<view class="button-list">
				<button class="u-reset-button button" :class="{ 'button-s': hasPrevious }" @click="onPractise(1)">
					<text class="text">上一题</text>
				</button>
				<button class="u-reset-button button button-s" @click="onPractise(2)">
					<image class="icon" src="@/static/images/collect.png" />
					<text class="text">收藏</text>
				</button>
				<button class="u-reset-button button next-step" :class="{ 'button-s': hasNext }" @click="onPractise(3)">
					<text class="text">下一题</text>
					<text class="value">{{ nextProgressText }}</text>
				</button>
			</view>
			<u-safe-bottom />
		</view>
	</view>
</template>

<script>
export default {
	components: {},
	data: () => ({
		// 题目数组
		questionList: [
			{
				id: 1,
				type: '单选题',
				schedule: '1-1',
				content: '1、写一篇描写课间操场景的作文，运用 "七种武器"，以下哪项能使作文内容更丰富呢？（ ）',
				options: [
					{ label: 'A', content: '只写同学们在做课间操的动作' },
					{ label: 'B', content: '从看到同学们整齐的队列、听到广播里的音乐声、闻到操场边树木的气息、摸到做操用的器材等多方面去写，同时写自己充满活力的心情' },
					{ label: 'C', content: '只写自己想象中下次课间操想怎么改进' },
					{ label: 'D', content: '只写看到的几个同学打闹的样子' }
				],
				correctAnswer: 'B',
				analysis: '写作文时运用"七种武器"（视觉、听觉、嗅觉、触觉、味觉、心理感受、想象）能让文章更加生动丰富。选项B正确地体现了多感官描写的特点，通过看、听、闻、摸等多个角度来描写课间操场景，同时加入心理感受，这样的写作方式能使文章内容更加丰富立体。',
				userAnswer: '', // 用户选择的答案
				hasAnswered: false, // 是否已答题
				showAnalysis: false // 是否显示解析
			},
			{
				id: 2,
				type: '单选题',
				schedule: '1-2',
				content: '2、在描写人物时，以下哪种方法最能突出人物的性格特点？（ ）',
				options: [
					{ label: 'A', content: '只描写人物的外貌特征' },
					{ label: 'B', content: '通过人物的语言、动作、心理活动等多方面来刻画' },
					{ label: 'C', content: '只写人物做了什么事情' },
					{ label: 'D', content: '只描写人物的穿着打扮' }
				],
				correctAnswer: 'B',
				analysis: '人物描写要立体化，需要通过多个维度来刻画。语言描写能体现人物的文化水平和性格；动作描写能反映人物的习惯和特点；心理活动描写能展现人物的内心世界。综合运用这些方法，才能塑造出鲜活的人物形象。',
				userAnswer: '',
				hasAnswered: false,
				showAnalysis: false
			},
			{
				id: 3,
				type: '单选题',
				schedule: '1-3',
				content: '3、写记叙文时，以下哪种开头方式最能吸引读者？（ ）',
				options: [
					{ label: 'A', content: '直接说明时间、地点、人物' },
					{ label: 'B', content: '用生动的场景描写或引人深思的问题开头' },
					{ label: 'C', content: '先介绍文章要写什么内容' },
					{ label: 'D', content: '用"今天我要写..."这样的句式' }
				],
				correctAnswer: 'B',
				analysis: '好的开头应该能够吸引读者的注意力，激发阅读兴趣。生动的场景描写能让读者身临其境，引人深思的问题能激发读者的好奇心。这样的开头比平铺直叙更有吸引力，能让文章从一开始就抓住读者的心。',
				userAnswer: '',
				hasAnswered: false,
				showAnalysis: false
			}
		],
		// 当前题目索引
		currentQuestionIndex: 0,
		// 总题目数量
		totalQuestions: 3
	}),
	computed: {
		// 当前题目数据
		currentQuestion() {
			return this.questionList[this.currentQuestionIndex];
		},
		// 是否有上一题
		hasPrevious() {
			return this.currentQuestionIndex > 0;
		},
		// 是否有下一题
		hasNext() {
			return this.currentQuestionIndex < this.questionList.length - 1;
		},
		// 当前进度显示
		progressText() {
			return `${this.currentQuestionIndex + 1} / ${this.questionList.length}`;
		},
		// 下一题进度显示
		nextProgressText() {
			return `${this.currentQuestionIndex + 2}/${this.questionList.length}`;
		},
		// 已答题数量
		answeredCount() {
			return this.questionList.filter(q => q.hasAnswered).length;
		},
		// 正确答题数量
		correctCount() {
			return this.questionList.filter(q => q.hasAnswered && q.userAnswer === q.correctAnswer).length;
		}
	},
	methods: {
		// 选择答案
		selectAnswer(option) {
			const currentQ = this.currentQuestion;
			if (currentQ.hasAnswered) return; // 已经答题则不能再选择

			// 记录用户答案到当前题目
			currentQ.userAnswer = option.label;
			currentQ.hasAnswered = true;
			currentQ.showAnalysis = true;
		},

		// 判断选项样式类
		getOptionClass(option) {
			const currentQ = this.currentQuestion;
			if (!currentQ.hasAnswered) return '';

			if (option.label === currentQ.correctAnswer) {
				// 正确答案显示绿色
				return 'option-correct';
			} else if (option.label === currentQ.userAnswer) {
				// 用户选择的错误答案显示红色
				return 'option-wrong';
			}
			return '';
		},

		// 上一题
		goToPrevious() {
			if (this.hasPrevious) {
				this.currentQuestionIndex--;
			}
		},

		// 下一题
		goToNext() {
			if (this.hasNext) {
				this.currentQuestionIndex++;
			}
		},

		// 重置当前题目答题状态
		resetCurrentAnswer() {
			const currentQ = this.currentQuestion;
			currentQ.userAnswer = '';
			currentQ.hasAnswered = false;
			currentQ.showAnalysis = false;
		},

		// 反馈功能
		onFeedback() {
			uni.showToast({
				title: '反馈功能',
				icon: 'none'
			});
		},

		// 练习功能（上一题/下一题/收藏）
		onPractise(type) {
			switch (type) {
				case 1:
					// 上一题
					if (this.hasPrevious) {
						this.goToPrevious();
					} else {
						uni.showToast({
							title: '已经是第一题了',
							icon: 'none'
						});
					}
					break;
				case 2:
					// 收藏功能
					uni.showToast({
						title: '收藏成功',
						icon: 'success'
					});
					break;
				case 3:
					// 下一题
					if (this.hasNext) {
						this.goToNext();
					} else {
						uni.showToast({
							title: '已经是最后一题了',
							icon: 'none'
						});
					}
					break;
			}
		},

		// 查看答题记录
		viewAnswerRecord() {
			const record = this.questionList.map(q => ({
				id: q.id,
				schedule: q.schedule,
				userAnswer: q.userAnswer,
				correctAnswer: q.correctAnswer,
				isCorrect: q.userAnswer === q.correctAnswer,
				hasAnswered: q.hasAnswered
			}));

			console.log('答题记录：', record);
			console.log(`总题数：${this.questionList.length}`);
			console.log(`已答题：${this.answeredCount}`);
			console.log(`正确数：${this.correctCount}`);
			console.log(`正确率：${this.answeredCount > 0 ? (this.correctCount / this.answeredCount * 100).toFixed(1) : 0}%`);

			uni.showModal({
				title: '答题统计',
				content: `已答题：${this.answeredCount}/${this.questionList.length}\n正确数：${this.correctCount}\n正确率：${this.answeredCount > 0 ? (this.correctCount / this.answeredCount * 100).toFixed(1) : 0}%`,
				showCancel: false
			});
		}
	},
	// 页面周期函数--监听页面加载
	onLoad(options) {

	},
	// 页面周期函数--监听页面初次渲染完成
	onReady() { },
	// 页面周期函数--监听页面显示(not-nvue)
	onShow() {

	},
	// 页面周期函数--监听页面隐藏
	onHide() { },
	// 页面周期函数--监听页面卸载
	onUnload() { },
	// 页面处理函数--监听用户上拉触底
	onReachBottom() { },
	// 页面处理函数--监听用户下拉动作
	onPullDownRefresh() { },
	// 页面周期函数--监听页面返回
	onBackPress() { },
	// 页面处理函数--监听页面滚动(not-nvue)
	onPageScroll(e) { },
	// 页面处理函数--用户点击右上角分享好友
	onShareAppMessage(options) { },
	// 页面处理函数--用户点击右上角分享朋友圈
	onShareTimeline(options) { }
};
</script>
<style lang="scss" scoped>
.practise-content {
	width: 100%;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-image: linear-gradient(to bottom, #FFD101 10%, #FFF 50%);

	.u-nav-slot {
		display: flex;
		align-items: center;

		.arrow {
			width: 30rpx;
			height: 30rpx;
		}

		.label {
			font-size: 36rpx;
			font-weight: 600;
			color: #000000;
			margin: 0 15rpx 0 5rpx;
		}

		.value {
			font-size: 30rpx;
			font-weight: 500;
			color: #000000;
		}
	}

	.practise-box {
		width: 100%;
		background-color: #FFFFFF;
		border-radius: 43rpx 43rpx 0 0;
		margin-top: 15rpx;
		display: flex;
		flex-direction: column;
		padding: 40rpx 25rpx;

		.practise-type {
			width: 100%;
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;

			.type {
				height: 58rpx;
				font-size: 30rpx;
				color: #000000;
				line-height: 58rpx;
				padding: 0 17rpx;
				background-color: #FFF8D8;
				border-radius: 7rpx;
				border: 2rpx solid #FFE674;
				margin-right: 25rpx;
			}

			.schedule {
				font-size: 30rpx;
				color: #464646;
			}
		}

		.content {
			width: 100%;
			font-size: 36rpx;
			font-weight: 600;
			color: #000000;
			line-height: 52rpx;
			margin-bottom: 30rpx;
		}

		.options-list {
			width: 100%;
			display: flex;
			flex-direction: column;

			.options-li {
				width: 100%;
				display: flex;
				flex-wrap: wrap;
				padding: 25rpx 0;
				border-radius: 20rpx;
				transition: all 150ms cubic-bezier(.36, .66, .04, 1);

				&:active {
					.option-content {
						text-decoration: underline wavy #c0c4cc;
					}

					background-color: #f9f9f9;
				}

				&.option-correct {
					.option-label {
						background-color: #65BC17;
						border-color: #65BC17;
						color: #FFFFFF;
					}

					.option-content {
						color: #55B300;
					}
				}

				&.option-wrong {

					.option-label {
						background-color: #F4621A;
						border-color: #F4621A;
						color: #FFFFFF;
					}

					// .option-content {
					// 	color: #F44336;
					// }
				}

				.option-label {
					width: 50rpx;
					height: 50rpx;
					font-size: 36rpx;
					line-height: 50rpx;
					text-align: center;
					box-sizing: border-box;
					color: #000000;
					border-radius: 46rpx;
					margin-right: 20rpx;
					background-color: #FFF;
					border: 2rpx solid #B2B2B2;
					transition: all 150ms cubic-bezier(.36, .66, .04, 1);
				}

				.option-content {
					width: 630rpx;
					font-size: 36rpx;
					color: #464646;
					line-height: 50rpx;
					transition: all 150ms cubic-bezier(.36, .66, .04, 1);
				}
			}
		}

		.correct-answer {
			width: 100%;
			height: 86rpx;
			background-color: #E9F8D1;
			border-radius: 10rpx;
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			margin: 40rpx 0;

			.label {
				font-size: 36rpx;
				line-height: 86rpx;
				color: #459101;
			}

			.value {
				font-size: 36rpx;
				line-height: 40rpx;
				font-weight: bold;
				color: #459101;
			}
		}
	}

	.answer-analysis {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 20rpx 25rpx;

		.analysis-title {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 35rpx;

			.title {
				font-size: 30rpx;
				font-weight: 600;
				color: #000000;
				z-index: 10;
			}

			.icon {
				width: 126rpx;
				height: 15rpx;
				background-image: linear-gradient(to right, #FFD101 0%, #FFFFFF 100%);
				border-radius: 8rpx;
				position: relative;
				bottom: 10rpx;
			}
		}

		.analysis-content {
			font-size: 36rpx;
			color: #000000;
			line-height: 52rpx;
		}
	}

	.feedback {
		position: fixed;
		left: 25rpx;
		bottom: 240rpx;
		width: 68rpx;
		display: flex;
		align-items: center;
		flex-direction: column;
		transition: all 100ms cubic-bezier(.36, .66, .04, 1);

		&:active {
			transform: scale(0.99);
		}

		.icon {
			width: 68rpx;
			height: 62rpx;
		}

		.text {
			font-size: 24rpx;
			color: #464646;
		}
	}

	.position-bottom {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 25rpx 20rpx;
		background-color: #FFF;
		box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);
		border-top: 1rpx solid #e4e7ed;

		.button-list {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.button {
				width: 186rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #DDDDDD;
				border-radius: 21rpx;
				transition: all 150ms cubic-bezier(.36, .66, .04, 1);
				margin: 0;

				.icon {
					width: 50rpx;
					height: 50rpx;
					margin-right: 10rpx;
				}

				.text {
					font-size: 32rpx;
					font-weight: 500;
					color: #B2B2B2;
				}

				.value {
					font-size: 30rpx;
					font-weight: 500;
					color: #464646;
				}

				&.button-s {
					background-color: #FFD101;
					box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);

					.text {
						color: #000;
					}

					&:active {
						background-color: #f7ca00;
					}
				}

				&.next-step {
					min-width: 272rpx;
					padding: 0 3rpx;
				}
			}
		}
	}
}
</style>
