.flex.data-v-43e462dc {
  display: flex;
}
.column.data-v-43e462dc {
  flex-direction: column;
}
.center.data-v-43e462dc {
  align-items: center;
}
.space-between.data-v-43e462dc {
  justify-content: space-between;
}
.activation-content.data-v-43e462dc {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #FFF;
}
.activation-content .u-nav-slot.data-v-43e462dc {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}
.activation-content .activation-box.data-v-43e462dc {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 43rpx 43rpx 0 0;
  position: relative;
  bottom: 33rpx;
}
.activation-content .activation-box .box-title.data-v-43e462dc {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  margin: 140rpx 0 70rpx;
}
.activation-content .activation-box .activation-input.data-v-43e462dc {
  width: 660rpx;
  height: 103rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 15rpx;
  padding: 0rpx 40rpx;
  border: 3rpx solid #B2B2B2;
  transition: all 0.3s ease;
}
.activation-content .activation-box .activation-input.input-focused.data-v-43e462dc {
  border-color: #FFD101;
  box-shadow: 0 0 0 2rpx rgba(255, 209, 1, 0.2);
}
.activation-content .activation-box .activation-input.input-filled.data-v-43e462dc {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2rpx rgba(76, 175, 80, 0.2);
}
.activation-content .activation-box .activation-input .input.data-v-43e462dc {
  height: 90rpx;
  width: 100%;
  font-size: 42rpx;
  color: #000000;
}
.activation-content .activation-box .activation-input .input-counter.data-v-43e462dc {
  font-size: 28rpx;
  color: #999;
  margin-left: 10rpx;
  min-width: 80rpx;
  text-align: right;
}
.activation-content .activation-box .activation-input .input-counter.counter-complete.data-v-43e462dc {
  color: #4CAF50;
  font-weight: bold;
}
.activation-content .activation-box .placeholder.data-v-43e462dc {
  font-size: 30rpx;
  color: #B2B2B2;
  margin: 20rpx 0 104rpx;
}
.activation-content .activation-box .button.data-v-43e462dc {
  width: 356rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #000000;
  font-weight: 600;
  background-color: #FFD101;
  border-radius: 21rpx;
}
.activation-content .overlay-content.data-v-43e462dc {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.activation-content .overlay-content .overlay-box.data-v-43e462dc {
  width: 650rpx;
  background-color: #FFF;
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 45rpx 65rpx;
}
.activation-content .overlay-content .overlay-box .box-title.data-v-43e462dc {
  font-size: 32rpx;
  color: #000000;
  font-weight: 600;
  margin-bottom: 80rpx;
}
.activation-content .overlay-content .overlay-box .placeholder.data-v-43e462dc {
  font-size: 30rpx;
  color: #000000;
}
.activation-content .overlay-content .overlay-box .box-item.data-v-43e462dc {
  width: 585rpx;
  height: 210rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #FFF9DC;
  border-radius: 9rpx 9rpx 9rpx 9rpx;
  border: 2rpx dashed #FFD101;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.1), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.1);
  gap: 36rpx;
  margin: 24rpx 0 54rpx;
}
.activation-content .overlay-content .overlay-box .box-item .value.data-v-43e462dc {
  font-size: 34rpx;
  color: #000000;
}
.activation-content .overlay-content .overlay-box .box-item .label.data-v-43e462dc {
  font-size: 30rpx;
  color: #B2B2B2;
}
.activation-content .overlay-content .overlay-box .submit-feedback.data-v-43e462dc {
  width: 370rpx;
  height: 104rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFD101;
  border-radius: 21rpx;
  font-size: 36rpx;
  color: #000000;
  margin-top: 60rpx;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.activation-content .overlay-content .overlay-box .submit-feedback.data-v-43e462dc:active {
  background-color: #f7ca00;
}
.activation-content .overlay-content .close-icon.data-v-43e462dc {
  width: 85rpx;
  height: 85rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;
  border-radius: 50%;
  border: 4px solid #000;
  background-color: #B2B2B2;
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.activation-content .overlay-content .close-icon.data-v-43e462dc:active {
  background-color: #9f9f9f;
}
