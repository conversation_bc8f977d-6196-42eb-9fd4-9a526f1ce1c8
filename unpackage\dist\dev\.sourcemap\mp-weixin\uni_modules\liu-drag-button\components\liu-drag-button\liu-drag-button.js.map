{"version": 3, "sources": ["webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue?e348", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue?2ee0", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue?af51", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue?9103", "uni-app:///uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue?6bfd", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue?ef95"], "names": ["props", "disabled", "type", "default", "canDocking", "bottomPx", "rightPx", "data", "left", "top", "isRemove", "windowWidth", "windowHeight", "btnWidth", "btnHeight", "x", "y", "old", "mounted", "methods", "getSysInfo", "view", "onChange", "touchstart", "touchend", "clickBtn"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACqC;;;AAGnG;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAorB,CAAgB,ysBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCaxsB;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAF;QACAC;MACA;IACA;EACA;EACAE;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;MACA;MACAC;QACA;QACA;QACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;UACA;YACA;UACA;QACA;UACA;YACA;UACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAA4/B,CAAgB,s/BAAG,EAAC,C;;;;;;;;;;;ACAhhC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./liu-drag-button.vue?vue&type=template&id=5860b122&scoped=true&\"\nvar renderjs\nimport script from \"./liu-drag-button.vue?vue&type=script&lang=js&\"\nexport * from \"./liu-drag-button.vue?vue&type=script&lang=js&\"\nimport style0 from \"./liu-drag-button.vue?vue&type=style&index=0&id=5860b122&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5860b122\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./liu-drag-button.vue?vue&type=template&id=5860b122&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./liu-drag-button.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./liu-drag-button.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<movable-area class=\"movable-area\" :scale-area=\"false\">\r\n\t\t\t<movable-view class=\"movable-view\" :class=\"!isRemove?'animation-info':''\" style=\"pointer-events: auto;\"\r\n\t\t\t\t@click=\"clickBtn\" @touchstart=\"touchstart\" @touchend=\"touchend\" @change=\"onChange\" direction=\"all\"\r\n\t\t\t\tinertia=\"true\" :x=\"x\" :y=\"y\" :disabled=\"disabled\" :out-of-bounds=\"true\" :damping=\"200\" :friction=\"100\">\r\n\t\t\t\t<slot></slot>\r\n\t\t\t</movable-view>\r\n\t\t</movable-area>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\t//是否禁用拖动\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t//是否自动停靠\r\n\t\t\tcanDocking: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t//按钮默认位置离底部距离（px）\r\n\t\t\tbottomPx: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 30\r\n\t\t\t},\r\n\t\t\t//按钮默认位置离右边距离（px）\r\n\t\t\trightPx: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tleft: 0,\r\n\t\t\t\ttop: 0,\r\n\t\t\t\tisRemove: true,\r\n\t\t\t\twindowWidth: 0,\r\n\t\t\t\twindowHeight: 0,\r\n\t\t\t\tbtnWidth: 0,\r\n\t\t\t\tbtnHeight: 0,\r\n\t\t\t\tx: 5,\r\n\t\t\t\ty: 1000,\r\n\t\t\t\told: {\r\n\t\t\t\t\tx: 0,\r\n\t\t\t\t\ty: 0\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.getSysInfo()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetSysInfo() {\r\n\t\t\t\tlet sysInfo = uni.getSystemInfoSync()\r\n\t\t\t\tthis.windowWidth = sysInfo.windowWidth\r\n\t\t\t\tthis.windowHeight = sysInfo.windowHeight\n\t\t\t\tlet view = uni.createSelectorQuery().in(this).select(\".movable-view\")\n\t\t\t\tview.boundingClientRect(rect => {\n\t\t\t\t\tthis.btnWidth = rect.width\n\t\t\t\t\tthis.btnHeight = rect.height\n\t\t\t\t\tthis.x = this.old.x\n\t\t\t\t\tthis.y = this.old.y\n\t\t\t\t\tthis.$nextTick(res => {\n\t\t\t\t\t\tthis.x = this.rightPx\n\t\t\t\t\t\tthis.y = this.windowHeight - this.btnHeight - this.bottomPx\n\t\t\t\t\t})\n\t\t\t\t}).exec()\r\n\t\t\t},\r\n\t\t\t//移动按钮\r\n\t\t\tonChange(e) {\r\n\t\t\t\tthis.old.x = e.detail.x\r\n\t\t\t\tthis.old.y = e.detail.y\r\n\t\t\t},\r\n\t\t\t//开始移动\r\n\t\t\ttouchstart(e) {\r\n\t\t\t\tthis.isRemove = true\r\n\t\t\t},\r\n\t\t\t//结束移动\r\n\t\t\ttouchend(e) {\r\n\t\t\t\tif (this.canDocking && this.old.x) {\r\n\t\t\t\t\tthis.x = this.old.x\r\n\t\t\t\t\tthis.y = this.old.y\r\n\t\t\t\t\tlet bWidth = (this.windowWidth - this.btnWidth) / 2\r\n\t\t\t\t\tif (this.x < 0 || (this.x > 0 && this.x <= bWidth)) {\r\n\t\t\t\t\t\tthis.$nextTick(res => {\r\n\t\t\t\t\t\t\tthis.x = 0\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$nextTick(res => {\r\n\t\t\t\t\t\t\tthis.x = this.windowWidth - this.btnWidth\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.isRemove = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//点击按钮\r\n\t\t\tclickBtn() {\r\n\t\t\t\tthis.$emit('clickBtn')\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n\t.movable-view {\r\n\t\twidth: 100rpx;\r\n\t}\r\n\r\n\t.animation-info {\r\n\t\ttransition: left .25s ease;\r\n\t}\r\n\r\n\t.movable-area {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: 999999 !important;\r\n\t\tpointer-events: none;\r\n\t}\r\n</style>\n", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./liu-drag-button.vue?vue&type=style&index=0&id=5860b122&scoped=true&lang=css&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./liu-drag-button.vue?vue&type=style&index=0&id=5860b122&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749956143749\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}