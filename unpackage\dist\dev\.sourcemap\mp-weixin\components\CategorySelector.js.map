{"version": 3, "sources": ["webpack:///E:/KingToyo/app_tixiaomeng/components/CategorySelector.vue?ed8b", "webpack:///E:/KingToyo/app_tixiaomeng/components/CategorySelector.vue?f83d", "webpack:///E:/KingToyo/app_tixiaomeng/components/CategorySelector.vue?7b5e", "webpack:///E:/KingToyo/app_tixiaomeng/components/CategorySelector.vue?b9f0", "uni-app:///components/CategorySelector.vue", "webpack:///E:/KingToyo/app_tixiaomeng/components/CategorySelector.vue?1aed", "webpack:///E:/KingToyo/app_tixiaomeng/components/CategorySelector.vue?2014"], "names": ["name", "props", "visible", "type", "default", "data", "currentLevel", "selectedItems", "scrollTop", "computed", "currentLevelData", "currentData", "breadcrumbItems", "items", "watch", "methods", "selectItem", "keysToDelete", "setTimeout", "goBack", "goToLevel", "nextStep", "uni", "title", "icon", "<PERSON><PERSON><PERSON>", "finalSelection", "getSelectedPath", "path", "resetSelector", "closeSelector", "onOverlayClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAqrB,CAAgB,0sBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC+CzsB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QAAA;MAAA;IACA;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;QACA;MACA;MAEA;MACA;QACA;QACA;UACAC;QACA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;IACA;EACA;EACAC;IACAZ;MACA;QACA;MACA;IACA;EACA;EACAa;IACA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QAAA;MAAA;MACAC;QACA;MACA;;MAEA;MACA;QACAC;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;QACA;;QAEA;QACA;UAAA;QAAA;QACAF;UACA;QACA;MACA;IACA;IAEA;IACAG;MAAA;MACA;QACA;QACA;;QAEA;QACA;UAAA;QAAA;QACAH;UACA;QACA;MACA;IACA;IAEA;IACAI;MACA;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAjB;QACAkB;QACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACvMA;AAAA;AAAA;AAAA;AAAozC,CAAgB,ixCAAG,EAAC,C;;;;;;;;;;;ACAx0C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/CategorySelector.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./CategorySelector.vue?vue&type=template&id=536f45d8&scoped=true&\"\nvar renderjs\nimport script from \"./CategorySelector.vue?vue&type=script&lang=js&\"\nexport * from \"./CategorySelector.vue?vue&type=script&lang=js&\"\nimport style0 from \"./CategorySelector.vue?vue&type=style&index=0&id=536f45d8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"536f45d8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/CategorySelector.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./CategorySelector.vue?vue&type=template&id=536f45d8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.visible\n    ? _vm.__map(_vm.currentLevelData, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = item.children && item.children.length > 0\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./CategorySelector.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./CategorySelector.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"category-selector-overlay\" v-if=\"visible\" @click=\"onOverlayClick\">\n\t\t<view class=\"category-selector\" @click.stop>\n\t\t\t<!-- 标题 -->\n\t\t\t<view class=\"selector-header\">\n\t\t\t\t<text class=\"header-title\">请选择激活内容</text>\n\t\t\t</view>\n\n\t\t\t<!-- 面包屑导航 -->\n\t\t\t<view class=\"breadcrumb\">\n\t\t\t\t<text class=\"breadcrumb-label\">已选：</text>\n\t\t\t\t<view class=\"breadcrumb-items\">\n\t\t\t\t\t<text v-for=\"(item, index) in breadcrumbItems\" :key=\"index\" class=\"breadcrumb-item\" @click=\"goToLevel(index)\">\n\t\t\t\t\t\t{{ item.name }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 列表容器 -->\n\t\t\t<view class=\"list-container\">\n\t\t\t\t<scroll-view class=\"category-list\" scroll-y=\"true\" :scroll-top=\"scrollTop\">\n\t\t\t\t\t<view v-for=\"(item, index) in currentLevelData\" :key=\"item.id\" class=\"category-item\"\n\t\t\t\t\t\t:class=\"{ 'item-selected': selectedItems[currentLevel] && selectedItems[currentLevel].id === item.id }\"\n\t\t\t\t\t\t@click=\"selectItem(item, index)\">\n\t\t\t\t\t\t<text class=\"item-text\">{{ item.name }}</text>\n\t\t\t\t\t\t<view class=\"item-arrow\" v-if=\"item.children && item.children.length > 0\">\n\t\t\t\t\t\t\t<text class=\"arrow-icon\">></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\n\t\t\t<!-- 提示文字 -->\n\t\t\t<view class=\"hint-text\">\n\t\t\t\t<text class=\"hint\">请滑动选择要激活的内容</text>\n\t\t\t</view>\n\n\t\t\t<!-- 操作按钮 -->\n\t\t\t<view class=\"action-buttons\">\n\t\t\t\t<button class=\"btn btn-back\" @click=\"goBack\" v-if=\"currentLevel > 0\">返回</button>\n\t\t\t\t<button class=\"btn btn-next\" @click=\"nextStep\">下一步</button>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tname: 'CategorySelector',\n\tprops: {\n\t\tvisible: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tdata: {\n\t\t\ttype: Array,\n\t\t\tdefault: () => []\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcurrentLevel: 0, // 当前层级\n\t\t\tselectedItems: {}, // 每个层级选中的项目\n\t\t\tscrollTop: 0, // 滚动位置\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 当前层级的数据\n\t\tcurrentLevelData() {\n\t\t\tif (this.currentLevel === 0) {\n\t\t\t\treturn this.data;\n\t\t\t}\n\n\t\t\tlet currentData = this.data;\n\t\t\tfor (let i = 0; i < this.currentLevel; i++) {\n\t\t\t\tconst selectedItem = this.selectedItems[i];\n\t\t\t\tif (selectedItem && selectedItem.children) {\n\t\t\t\t\tcurrentData = selectedItem.children;\n\t\t\t\t} else {\n\t\t\t\t\treturn [];\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn currentData;\n\t\t},\n\n\t\t// 面包屑数据\n\t\tbreadcrumbItems() {\n\t\t\tconst items = [];\n\t\t\tfor (let i = 0; i <= this.currentLevel; i++) {\n\t\t\t\tif (this.selectedItems[i]) {\n\t\t\t\t\titems.push(this.selectedItems[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn items;\n\t\t}\n\t},\n\twatch: {\n\t\tvisible(newVal) {\n\t\t\tif (newVal) {\n\t\t\t\tthis.resetSelector();\n\t\t\t}\n\t\t}\n\t},\n\tmethods: {\n\t\t// 选择项目\n\t\tselectItem(item) {\n\t\t\t// 设置当前层级的选中项\n\t\t\tthis.$set(this.selectedItems, this.currentLevel, item);\n\n\t\t\t// 清除后续层级的选择\n\t\t\tconst keysToDelete = Object.keys(this.selectedItems).filter(key => parseInt(key) > this.currentLevel);\n\t\t\tkeysToDelete.forEach(key => {\n\t\t\t\tthis.$delete(this.selectedItems, key);\n\t\t\t});\n\n\t\t\t// 如果有子级，自动进入下一级\n\t\t\tif (item.children && item.children.length > 0) {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.currentLevel++;\n\t\t\t\t\tthis.scrollTop = 0;\n\t\t\t\t}, 150);\n\t\t\t}\n\t\t},\n\n\t\t// 返回上一级\n\t\tgoBack() {\n\t\t\tif (this.currentLevel > 0) {\n\t\t\t\tthis.currentLevel--;\n\t\t\t\tthis.scrollTop = 0;\n\n\t\t\t\t// 清除当前层级及后续层级的选择\n\t\t\t\tconst keysToDelete = Object.keys(this.selectedItems).filter(key => parseInt(key) >= this.currentLevel);\n\t\t\t\tkeysToDelete.forEach(key => {\n\t\t\t\t\tthis.$delete(this.selectedItems, key);\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 跳转到指定层级\n\t\tgoToLevel(level) {\n\t\t\tif (level < this.currentLevel) {\n\t\t\t\tthis.currentLevel = level;\n\t\t\t\tthis.scrollTop = 0;\n\n\t\t\t\t// 清除后续层级的选择\n\t\t\t\tconst keysToDelete = Object.keys(this.selectedItems).filter(key => parseInt(key) > level);\n\t\t\t\tkeysToDelete.forEach(key => {\n\t\t\t\t\tthis.$delete(this.selectedItems, key);\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 下一步\n\t\tnextStep() {\n\t\t\tconst selectedPath = this.getSelectedPath();\n\t\t\tif (selectedPath.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择内容',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.$emit('confirm', {\n\t\t\t\tselectedItems: this.selectedItems,\n\t\t\t\tselectedPath: selectedPath,\n\t\t\t\tfinalSelection: this.selectedItems[this.currentLevel]\n\t\t\t});\n\t\t\tthis.closeSelector();\n\t\t},\n\n\t\t// 获取选择路径\n\t\tgetSelectedPath() {\n\t\t\tconst path = [];\n\t\t\tfor (let i = 0; i <= this.currentLevel; i++) {\n\t\t\t\tif (this.selectedItems[i]) {\n\t\t\t\t\tpath.push(this.selectedItems[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn path;\n\t\t},\n\n\t\t// 重置选择器\n\t\tresetSelector() {\n\t\t\tthis.currentLevel = 0;\n\t\t\tthis.selectedItems = {};\n\t\t\tthis.scrollTop = 0;\n\t\t},\n\n\t\t// 关闭选择器\n\t\tcloseSelector() {\n\t\t\tthis.$emit('close');\n\t\t},\n\n\t\t// 点击遮罩层\n\t\tonOverlayClick() {\n\t\t\tthis.closeSelector();\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.category-selector-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 9999;\n}\n\n.category-selector {\n\twidth: 600rpx;\n\tbackground-color: #FFFFFF;\n\tborder-radius: 20rpx;\n\tpadding: 40rpx 30rpx;\n\tmargin: 0 40rpx;\n\tmax-height: 80vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n// 标题\n.selector-header {\n\ttext-align: center;\n\tmargin-bottom: 30rpx;\n\n\t.header-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t}\n}\n\n// 面包屑\n.breadcrumb {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n\tmin-height: 40rpx;\n\n\t.breadcrumb-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tmargin-right: 10rpx;\n\t}\n\n\t.breadcrumb-items {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.breadcrumb-item {\n\t\tfont-size: 28rpx;\n\t\tcolor: #FFD101;\n\t\tmargin-right: 10rpx;\n\t\tcursor: pointer;\n\n\t\t&:not(:last-child)::after {\n\t\t\tcontent: ' - ';\n\t\t\tcolor: #999;\n\t\t\tmargin-left: 10rpx;\n\t\t}\n\n\t\t&:hover {\n\t\t\ttext-decoration: underline;\n\t\t}\n\t}\n}\n\n// 列表容器\n.list-container {\n\tflex: 1;\n\tmargin-bottom: 20rpx;\n\tborder: 2rpx solid #E5E5E5;\n\tborder-radius: 15rpx;\n\toverflow: hidden;\n}\n\n.category-list {\n\theight: 400rpx;\n\tbackground-color: #F8F8F8;\n}\n\n.category-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 25rpx 30rpx;\n\tbackground-color: #FFFFFF;\n\tborder-bottom: 1rpx solid #E5E5E5;\n\ttransition: all 0.2s ease;\n\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t&.item-selected {\n\t\tbackground-color: #FFF8E1;\n\t\tborder-left: 6rpx solid #FFD101;\n\t}\n\n\t&:active {\n\t\tbackground-color: #F5F5F5;\n\t}\n\n\t.item-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #333;\n\t\tflex: 1;\n\t}\n\n\t.item-arrow {\n\t\twidth: 30rpx;\n\t\theight: 30rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\n\t\t.arrow-icon {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #999;\n\t\t\tfont-weight: bold;\n\t\t}\n\t}\n}\n\n// 提示文字\n.hint-text {\n\ttext-align: center;\n\tmargin-bottom: 30rpx;\n\n\t.hint {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999;\n\t}\n}\n\n// 操作按钮\n.action-buttons {\n\tdisplay: flex;\n\tgap: 20rpx;\n\n\t.btn {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tborder-radius: 15rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tborder: none;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.2s ease;\n\n\t\t&.btn-back {\n\t\t\tbackground-color: #F5F5F5;\n\t\t\tcolor: #666;\n\n\t\t\t&:active {\n\t\t\t\tbackground-color: #E5E5E5;\n\t\t\t}\n\t\t}\n\n\t\t&.btn-next {\n\t\t\tbackground-color: #FFD101;\n\t\t\tcolor: #333;\n\n\t\t\t&:active {\n\t\t\t\tbackground-color: #E6BC00;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./CategorySelector.vue?vue&type=style&index=0&id=536f45d8&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./CategorySelector.vue?vue&type=style&index=0&id=536f45d8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749981517882\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}