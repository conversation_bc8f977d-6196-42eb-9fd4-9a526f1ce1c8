<template>
	<view class="wrong-content">
		<u-navbar :placeholder="true" bgColor="#FFD101">
			<view class="u-nav-slot" slot="left">全部错题本</view>
		</u-navbar>
		<u-gap height="50rpx" bgColor="#FFD101" />
		<view class="wrong-list">
			<text class="wrong-title">共{{ wrongList.length }}题</text>
			<view class="wrong-li" v-for="(item, index) in wrongList" :key="index">
				<text class="content u-line-2">{{ item.content }}</text>
			</view>
			<u-loadmore v-if="wrongList.length > 0" line :status="loadmore" />
		</view>
		<u-empty :show="isData" text="暂无错题本" marginTop="10rpx" />
		<u-safe-bottom />
		<view class="position-bottom">
			<view class="button-list">
				<button class="u-reset-button button" :class="{'button-s' : wrongList.length > 0}" @click="onPractise(1)">
					<text class="text">清空</text>
				</button>
				<button class="u-reset-button button" :class="{'button-s' : wrongList.length > 0}" @click="onPractise(2)">
					<text class="text">练习</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		components: {},
		data: () => ({
			loading: true, // 加载状态
			orderList: [], // 订单列表
			loadmore: 'loading', // 加载文字提示
			queryForm: {
				currentPage: 1, // 页码
				pageSize: 20, // 条数
				operType: 0, // 0查询，1导出
			},
			isData: false, // 是否有数据
			isRefresh: false, // 是否刷新
			wrongList: [
				{id: 1, content: '1、在写作文描述一场音乐会场景时，“我听到悠扬的乐曲声，仿佛自己置身于音乐的海洋”，这里体现了 “七种武器” 中描述一场音乐会场景的哪两'},
				{id: 2, content: '2、若想让作文里关于动物的描写更灵动，运用 “七种武器” 时，比较好的做法是（ ）'},
				{id: 3, content: '3、完成 “七种武器” 优美句子摘抄后进行仿写时，第一步应该做什么呢？（ ）'},
				{id: 4, content: '4、在运用 “七种武器” 写一篇描写春节场景的作文时，以下哪个方面可以体现 “想象” 这一武器呢？'},
				{id: 5, content: '5、如果遇到不太理解的地方，较好的做法是（ ）'},
			] // 错题列表	
		}),
		filters: {},
		methods: {
			// 操作
			onPractise(e) {
				if (this.wrongList.length > 0) {
					if (e == 1) {
						uni.showModal({
							title: '提示',
							content: '清空后无法恢复，确定清空错题本吗？',
							success: (res) => {
								if (res.confirm) {
									this.wrongList = []; // 清空错题列表
									this.isData = true; // 设置无数据状态
									uni.showToast({
										title: '已清空',
										icon: 'none'
									});
								}
							}
						})
					} else {
						// 跳转到练习页面
						uni.navigateTo({
							url: '/pages/index/practise'
						})
					}
				}
			},
		},
		// 页面周期函数--监听页面加载
		onLoad(options) {
			
		},
		// 页面周期函数--监听页面初次渲染完成
		onReady() {
			this.loadmore = 'nomore'
		},
		// 页面周期函数--监听页面显示(not-nvue)
		onShow() {
			
		},
		// 页面周期函数--监听页面隐藏
		onHide() {},
		// 页面周期函数--监听页面卸载
		onUnload() {},
		// 页面处理函数--监听用户上拉触底
		onReachBottom() {},
		// 页面处理函数--监听用户下拉动作
		onPullDownRefresh() {},
		// 页面周期函数--监听页面返回
		onBackPress() {},
		// 页面处理函数--监听页面滚动(not-nvue)
		onPageScroll(e) {},
		// 页面处理函数--用户点击右上角分享好友
		onShareAppMessage(options) {},
		// 页面处理函数--用户点击右上角分享朋友圈
		onShareTimeline(options) {}
	};
</script>
<style lang="scss" scoped>
	.wrong-content {
		width: 100%;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		background-image: linear-gradient(to bottom, #FFF 10%, #FFF 50%);
		.u-nav-slot {
			font-size: 36rpx;
			font-weight: 600;
			color: #000000;
		}
		.wrong-list {
			width: 100%;
			display: flex;
			flex-direction: column;
			padding: 38rpx 20rpx;
			box-sizing: border-box;
			background-color: #fff;
			border-radius: 10rpx;
			border-radius: 43rpx 43rpx 0 0;
			position: relative;
			bottom: 33rpx;
			margin-bottom: 180rpx;
			.wrong-title {
				width: 100%;
				font-size: 30rpx;
				color: #464646;
				padding-bottom: 33rpx;
				border-bottom: 1rpx solid #E8E8E8;
			}
			.wrong-li {
				width: 100%;
				padding: 35rpx 0;
				border-bottom: 1rpx solid #E8E8E8;
				transition: all 150ms cubic-bezier(.36, .66, .04, 1);
				&:active {
					background-color: #f5f5f5;
				}
				.content {
					font-size: 32rpx;
					color: #000000;
					line-height: 42rpx;
				}
			}
		}
		.position-bottom {
			position: fixed;
			left: 0;
			right: 0;
			bottom: 0;
			width: 100%;
			display: flex;
			flex-direction: column;
			padding: 25rpx 20rpx;
			background-color: #FFF;
			box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);
			border-top: 1rpx solid #e4e7ed;
			.button-list {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
		
				.button {
					width: 338rpx;
					height: 100rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: #DDDDDD;
					border-radius: 29rpx;
					transition: all 150ms cubic-bezier(.36, .66, .04, 1);
					margin: 0;
				
					.text {
						font-size: 32rpx;
						font-weight: 500;
						color: #B2B2B2;
					}
				
					&.button-s {
						background-color: #FFD101;
						box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);
						.text {
							color: #000;
						}
						&:active {
							background-color: #f7ca00;
						}
					}
				}
			}
		}
	}
</style>