.flex.data-v-59d936fe {
  display: flex;
}
.column.data-v-59d936fe {
  flex-direction: column;
}
.center.data-v-59d936fe {
  align-items: center;
}
.space-between.data-v-59d936fe {
  justify-content: space-between;
}
.practise-content.data-v-59d936fe {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom, #FFD101 10%, #FFF 50%);
}
.practise-content .u-nav-slot.data-v-59d936fe {
  display: flex;
  align-items: center;
}
.practise-content .u-nav-slot .arrow.data-v-59d936fe {
  width: 30rpx;
  height: 30rpx;
}
.practise-content .u-nav-slot .label.data-v-59d936fe {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  margin: 0 15rpx 0 5rpx;
}
.practise-content .u-nav-slot .value.data-v-59d936fe {
  font-size: 30rpx;
  font-weight: 500;
  color: #000000;
}
.practise-content .practise-box.data-v-59d936fe {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 43rpx 43rpx 0 0;
  margin-top: 15rpx;
  display: flex;
  flex-direction: column;
  padding: 40rpx 25rpx;
}
.practise-content .practise-box .practise-type.data-v-59d936fe {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.practise-content .practise-box .practise-type .type.data-v-59d936fe {
  height: 58rpx;
  font-size: 30rpx;
  color: #000000;
  line-height: 58rpx;
  padding: 0 17rpx;
  background-color: #FFF8D8;
  border-radius: 7rpx;
  border: 2rpx solid #FFE674;
  margin-right: 25rpx;
}
.practise-content .practise-box .practise-type .schedule.data-v-59d936fe {
  font-size: 30rpx;
  color: #464646;
}
.practise-content .practise-box .content.data-v-59d936fe {
  width: 100%;
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  line-height: 52rpx;
  margin-bottom: 30rpx;
}
.practise-content .practise-box .options-list.data-v-59d936fe {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.practise-content .practise-box .options-list .options-li.data-v-59d936fe {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 25rpx 0;
  border-radius: 20rpx;
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.practise-content .practise-box .options-list .options-li.data-v-59d936fe:active {
  background-color: #f9f9f9;
}
.practise-content .practise-box .options-list .options-li:active .option-content.data-v-59d936fe {
  -webkit-text-decoration: underline wavy #c0c4cc;
          text-decoration: underline wavy #c0c4cc;
}
.practise-content .practise-box .options-list .options-li.option-correct .option-label.data-v-59d936fe {
  background-color: #65BC17;
  border-color: #65BC17;
  color: #FFFFFF;
}
.practise-content .practise-box .options-list .options-li.option-correct .option-content.data-v-59d936fe {
  color: #55B300;
}
.practise-content .practise-box .options-list .options-li.option-wrong .option-label.data-v-59d936fe {
  background-color: #F4621A;
  border-color: #F4621A;
  color: #FFFFFF;
}
.practise-content .practise-box .options-list .options-li .option-label.data-v-59d936fe {
  width: 50rpx;
  height: 50rpx;
  font-size: 36rpx;
  line-height: 50rpx;
  text-align: center;
  box-sizing: border-box;
  color: #000000;
  border-radius: 46rpx;
  margin-right: 20rpx;
  background-color: #FFF;
  border: 2rpx solid #B2B2B2;
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.practise-content .practise-box .options-list .options-li .option-content.data-v-59d936fe {
  width: 630rpx;
  font-size: 36rpx;
  color: #464646;
  line-height: 50rpx;
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.practise-content .practise-box .correct-answer.data-v-59d936fe {
  width: 100%;
  height: 86rpx;
  background-color: #E9F8D1;
  border-radius: 10rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}
.practise-content .practise-box .correct-answer .label.data-v-59d936fe {
  font-size: 36rpx;
  line-height: 86rpx;
  color: #459101;
}
.practise-content .practise-box .correct-answer .value.data-v-59d936fe {
  font-size: 36rpx;
  line-height: 40rpx;
  font-weight: bold;
  color: #459101;
}
.practise-content .answer-analysis.data-v-59d936fe {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 20rpx 25rpx;
}
.practise-content .answer-analysis .analysis-title.data-v-59d936fe {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 35rpx;
}
.practise-content .answer-analysis .analysis-title .title.data-v-59d936fe {
  font-size: 30rpx;
  font-weight: 600;
  color: #000000;
  z-index: 10;
}
.practise-content .answer-analysis .analysis-title .icon.data-v-59d936fe {
  width: 126rpx;
  height: 15rpx;
  background-image: linear-gradient(to right, #FFD101 0%, #FFFFFF 100%);
  border-radius: 8rpx;
  position: relative;
  bottom: 10rpx;
}
.practise-content .answer-analysis .analysis-content.data-v-59d936fe {
  font-size: 36rpx;
  color: #000000;
  line-height: 52rpx;
}
.practise-content .feedback.data-v-59d936fe {
  position: fixed;
  left: 25rpx;
  bottom: 240rpx;
  width: 68rpx;
  display: flex;
  align-items: center;
  flex-direction: column;
  transition: all 100ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.practise-content .feedback.data-v-59d936fe:active {
  -webkit-transform: scale(0.99);
          transform: scale(0.99);
}
.practise-content .feedback .icon.data-v-59d936fe {
  width: 68rpx;
  height: 62rpx;
}
.practise-content .feedback .text.data-v-59d936fe {
  font-size: 24rpx;
  color: #464646;
}
.practise-content .position-bottom.data-v-59d936fe {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 25rpx 20rpx;
  background-color: #FFF;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);
  border-top: 1rpx solid #e4e7ed;
}
.practise-content .position-bottom .button-list.data-v-59d936fe {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.practise-content .position-bottom .button-list .button.data-v-59d936fe {
  width: 186rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #DDDDDD;
  border-radius: 21rpx;
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
  margin: 0;
}
.practise-content .position-bottom .button-list .button .icon.data-v-59d936fe {
  width: 50rpx;
  height: 50rpx;
  margin-right: 10rpx;
}
.practise-content .position-bottom .button-list .button .text.data-v-59d936fe {
  font-size: 32rpx;
  font-weight: 500;
  color: #B2B2B2;
}
.practise-content .position-bottom .button-list .button .value.data-v-59d936fe {
  font-size: 30rpx;
  font-weight: 500;
  color: #464646;
}
.practise-content .position-bottom .button-list .button.button-s.data-v-59d936fe {
  background-color: #FFD101;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);
}
.practise-content .position-bottom .button-list .button.button-s .text.data-v-59d936fe {
  color: #000;
}
.practise-content .position-bottom .button-list .button.button-s.data-v-59d936fe:active {
  background-color: #f7ca00;
}
.practise-content .position-bottom .button-list .button.button-collected.data-v-59d936fe {
  background-color: #FF6B35;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 107, 53, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 107, 53, 0.2);
}
.practise-content .position-bottom .button-list .button.button-collected .text.data-v-59d936fe {
  color: #FFF;
}
.practise-content .position-bottom .button-list .button.button-collected.data-v-59d936fe:active {
  background-color: #e55a2b;
}
.practise-content .position-bottom .button-list .button.next-step.data-v-59d936fe {
  min-width: 272rpx;
  padding: 0 3rpx;
}
.practise-content .feedback-overlay.data-v-59d936fe {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.practise-content .feedback-overlay .feedback-box.data-v-59d936fe {
  width: 650rpx;
  background-color: #FFF;
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 55rpx 50rpx 72rpx;
}
.practise-content .feedback-overlay .feedback-box .box-title.data-v-59d936fe {
  font-size: 36rpx;
  color: #000000;
  margin-bottom: 34rpx;
}
.practise-content .feedback-overlay .feedback-box .textarea-item.data-v-59d936fe {
  width: 100%;
}
.practise-content .feedback-overlay .feedback-box .submit-feedback.data-v-59d936fe {
  width: 370rpx;
  height: 104rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFD101;
  border-radius: 21rpx;
  font-size: 36rpx;
  color: #000000;
  margin-top: 60rpx;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.practise-content .feedback-overlay .feedback-box .submit-feedback.data-v-59d936fe:active {
  background-color: #f7ca00;
}
.practise-content .feedback-overlay .close-icon.data-v-59d936fe {
  width: 85rpx;
  height: 85rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;
  border-radius: 50%;
  border: 4px solid #000;
  background-color: #B2B2B2;
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.practise-content .feedback-overlay .close-icon.data-v-59d936fe:active {
  background-color: #9f9f9f;
}
