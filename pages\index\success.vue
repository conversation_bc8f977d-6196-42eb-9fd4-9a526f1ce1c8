<template>
	<view class="success-content">
		<!-- 顶部标题 -->
		<view class="header">
			<text class="title">太棒了!</text>
		</view>

		<!-- 成绩展示区域 -->
		<view class="score-section">
			<!-- 半圆形进度条 -->
			<view class="progress-container">
				<view class="progress-circle">
					<!-- 背景圆弧 -->
					<view class="progress-bg"></view>
					<!-- 进度圆弧 -->
					<view class="progress-bar" :style="{ transform: `rotate(${progressRotation}deg)` }"></view>
					<!-- 进度圆点 -->
					<view class="progress-dot" :style="{ transform: `rotate(${progressRotation}deg)` }"></view>
					<!-- 中心内容 -->
					<view class="progress-center">
						<text class="score-text">{{ animatedScore }}%</text>
						<text class="score-label">正确率</text>
					</view>
				</view>
			</view>

			<!-- 统计信息 -->
			<view class="stats-container">
				<view class="stats-item">
					<text class="stats-number">{{ answeredCount }}</text>
					<text class="stats-label">答题数</text>
				</view>
				<view class="stats-item">
					<text class="stats-number correct">{{ correctCount }}</text>
					<text class="stats-label">答对</text>
				</view>
				<view class="stats-item">
					<text class="stats-number wrong">{{ wrongCount }}</text>
					<text class="stats-label">答错</text>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="action-buttons">
			<button class="btn btn-primary" @click="checkOrder">进入错题本</button>
			<button class="btn btn-secondary" @click="returnHome">返回</button>
		</view>
	</view>
</template>

<script>
export default {
	components: {},
	data: () => ({
		answeredCount: '50', // 答题数量
		correctCount: '45', // 正确数
		wrongCount: '5', // 错题数
		score: '95', // 正确率
		animatedScore: 0, // 动画中的分数
		progressRotation: 0, // 进度条旋转角度（从0度开始，即左侧）
		animationTimer: null, // 动画定时器
	}),
	computed: {},
	methods: {
		// 开始进度条动画
		startProgressAnimation() {
			// 确保score是0-100之间的整数
			const targetScore = Math.max(0, Math.min(100, parseInt(this.score) || 0));
			const duration = 2000; // 动画持续时间2秒
			const steps = 60; // 动画步数
			const stepDuration = duration / steps;
			const scoreStep = targetScore / steps;

			let currentStep = 0;

			this.animationTimer = setInterval(() => {
				currentStep++;

				// 更新分数
				this.animatedScore = Math.round(scoreStep * currentStep);

				// 更新进度条角度
				// 半圆从左侧(-90度)到右侧(90度)，总共180度
				// 根据分数百分比计算角度：-90 + (score/100) * 180
				const currentScore = scoreStep * currentStep;
				this.progressRotation = -90 + (currentScore / 100) * 180;

				// 动画完成
				if (currentStep >= steps) {
					clearInterval(this.animationTimer);
					this.animatedScore = targetScore;
					this.progressRotation = (targetScore / 100) * 180;
				}
			}, stepDuration);
		},

		// 进入错题本
		checkOrder() {
			uni.navigateTo({
				url: '/pages/index/wrongBook'
			})
		},
		// 返回
		returnHome() {
			// 返回到首页或上一页
			let pages = getCurrentPages();
			if (pages.length > 1) {
				uni.navigateBack({ delta: 1 });
			} else {
				uni.switchTab({
					url: '/pages/index/index'
				});
			}
		},
		// 获取邀请信息
		async getInviteInfo() {
			try {
				const { code, data } = await invitation({})
				if (code === '00000') {
					this.inviteInfo = data
				}
			} catch (error) {
				console.error('获取邀请信息失败:', error)
				uni.showToast({
					icon: 'none',
					title: '获取邀请信息失败',
					duration: 2000
				})
			}
		},
		// 取消返回上一页
		openClose() {
			let pages = getCurrentPages();
			let currentPages = pages.length
			if (currentPages > 1) {
				let beforePage = pages[pages.length - 2]
				uni.navigateBack({ delta: 1 })
			} else {
				uni.switchTab({ url: "/pages/index/index" });
			}
		},
	},
	watch: {},
	// 页面周期函数--监听页面加载
	onLoad(options) {
		if (options.a) {
			this.answeredCount = options.a
		}
		if (options.c) {
			this.correctCount = options.c
		}
		if (options.w) {
			this.wrongCount = options.w
		}
		if (options.s) {
			// 确保score是0-100之间的整数
			this.score = Math.max(0, Math.min(100, parseInt(options.s) || 0)).toString()
		}
	},
	// 页面周期函数--监听页面初次渲染完成
	onReady() { },
	// 页面周期函数--监听页面显示(not-nvue)
	onShow() {
		// 延迟启动动画，让页面先渲染完成
		setTimeout(() => {
			this.startProgressAnimation();
		}, 300);
	},
	// 页面周期函数--监听页面隐藏
	onHide() { },
	// 页面周期函数--监听页面卸载
	onUnload() {
		// 清理定时器
		if (this.animationTimer) {
			clearInterval(this.animationTimer);
		}
	},
	// 页面处理函数--监听用户下拉动作
	onPullDownRefresh() { },
	// 页面处理函数--监听用户上拉触底
	onReachBottom() { },
	// 页面周期函数--监听页面返回
	onBackPress() { },
	// 页面处理函数--用户点击右上角分享好友
	onShareAppMessage(options) { },
};
</script>
<style lang="scss" scoped>
.success-content {
	width: 100%;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-sizing: border-box;

	// 顶部标题
	.header {
		width: 100%;
		height: 200rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-image: linear-gradient(to bottom, #FFD101, #FFFFFF);

		.title {
			font-size: 60rpx;
			font-weight: bold;
			color: #333;
			text-align: center;
		}
	}

	// 成绩展示区域
	.score-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 120rpx;
	}

	// 半圆形进度条容器
	.progress-container {
		margin-bottom: 80rpx;
		overflow: hidden;
	}

	// 进度圆环
	.progress-circle {
		position: relative;
		width: 416rpx;
		height: 208rpx;

		// 背景圆弧
		.progress-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 416rpx;
			height: 416rpx;
			border: 20rpx solid #FFED9C;
			border-radius: 50%;
			border-bottom: 20rpx solid transparent;
			clip: rect(0, 416rpx, 208rpx, 0);
		}

		// 进度圆弧
		.progress-bar {
			position: absolute;
			top: 0;
			left: 0;
			width: 416rpx;
			height: 416rpx;
			border: 20rpx solid #FFD101;
			border-radius: 50%;
			border-bottom: 20rpx solid transparent;
			clip: rect(0, 416rpx, 208rpx, 0);
			transform-origin: center center;
			transform: rotate(-90deg);
			transition: transform 0.1s ease-out;
		}

		// 进度圆点
		.progress-dot {
			position: absolute;
			top: 198rpx;
			left: 10rpx;
			width: 20rpx;
			height: 20rpx;
			background-color: #FFD101;
			border-radius: 50%;
			transform-origin: 198rpx -188rpx;
			transform: rotate(-90deg);
			transition: transform 0.1s ease-out;
			z-index: 5;
		}

		// 中心内容
		.progress-center {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -20%);
			text-align: center;
			z-index: 10;

			.score-text {
				display: block;
				font-size: 80rpx;
				font-weight: bold;
				color: #FFD101;
				line-height: 1;
				margin-bottom: 10rpx;
			}

			.score-label {
				display: block;
				font-size: 32rpx;
				color: #666;
			}
		}
	}

	// 进度条标记
	.progress-container {
		position: relative;

		&::before {
			content: '0';
			position: absolute;
			bottom: -40rpx;
			left: 20rpx;
			font-size: 28rpx;
			color: #999;
		}

		&::after {
			content: '100';
			position: absolute;
			bottom: -40rpx;
			right: 20rpx;
			font-size: 28rpx;
			color: #999;
		}
	}

	// 统计信息容器
	.stats-container {
		display: flex;
		justify-content: space-around;
		width: 100%;
		max-width: 600rpx;

		.stats-item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.stats-number {
				font-size: 60rpx;
				font-weight: bold;
				color: #FFD101;
				line-height: 1;
				margin-bottom: 10rpx;

				&.correct {
					color: #4CAF50;
				}

				&.wrong {
					color: #FF5722;
				}
			}

			.stats-label {
				font-size: 28rpx;
				color: #999;
			}
		}
	}

	// 操作按钮
	.action-buttons {
		display: flex;
		flex-direction: column;
		width: 100%;
		max-width: 600rpx;
		gap: 30rpx;

		.btn {
			width: 100%;
			height: 100rpx;
			border-radius: 50rpx;
			font-size: 36rpx;
			font-weight: 600;
			border: none;
			transition: all 0.3s ease;

			&.btn-primary {
				background: linear-gradient(135deg, #FFD101 0%, #FFA000 100%);
				color: #333;
				box-shadow: 0rpx 8rpx 20rpx rgba(255, 209, 1, 0.3);

				&:active {
					transform: translateY(2rpx);
					box-shadow: 0rpx 4rpx 10rpx rgba(255, 209, 1, 0.3);
				}
			}

			&.btn-secondary {
				background: #FFF;
				color: #FFD101;
				border: 2rpx solid #FFD101;

				&:active {
					background: #FFF8E1;
				}
			}
		}
	}


}
</style>
