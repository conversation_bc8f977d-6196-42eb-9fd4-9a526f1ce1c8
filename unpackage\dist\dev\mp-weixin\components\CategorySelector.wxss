.flex.data-v-536f45d8 {
  display: flex;
}
.column.data-v-536f45d8 {
  flex-direction: column;
}
.center.data-v-536f45d8 {
  align-items: center;
}
.space-between.data-v-536f45d8 {
  justify-content: space-between;
}
.category-selector-overlay.data-v-536f45d8 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.category-selector.data-v-536f45d8 {
  width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin: 0 40rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}
.selector-header.data-v-536f45d8 {
  text-align: center;
  margin-bottom: 30rpx;
}
.selector-header .header-title.data-v-536f45d8 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.breadcrumb.data-v-536f45d8 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  min-height: 40rpx;
}
.breadcrumb .breadcrumb-label.data-v-536f45d8 {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}
.breadcrumb .breadcrumb-items.data-v-536f45d8 {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.breadcrumb .breadcrumb-item.data-v-536f45d8 {
  font-size: 28rpx;
  color: #FFD101;
  margin-right: 10rpx;
  cursor: pointer;
}
.breadcrumb .breadcrumb-item.data-v-536f45d8:not(:last-child)::after {
  content: " - ";
  color: #999;
  margin-left: 10rpx;
}
.breadcrumb .breadcrumb-item.data-v-536f45d8:hover {
  text-decoration: underline;
}
.list-container.data-v-536f45d8 {
  flex: 1;
  margin-bottom: 20rpx;
  border: 2rpx solid #E5E5E5;
  border-radius: 15rpx;
  overflow: hidden;
}
.category-list.data-v-536f45d8 {
  height: 400rpx;
  background-color: #F8F8F8;
}
.category-item.data-v-536f45d8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 30rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E5E5E5;
  transition: all 0.2s ease;
}
.category-item.data-v-536f45d8:last-child {
  border-bottom: none;
}
.category-item.item-selected.data-v-536f45d8 {
  background-color: #FFF8E1;
  border-left: 6rpx solid #FFD101;
}
.category-item.data-v-536f45d8:active {
  background-color: #F5F5F5;
}
.category-item .item-text.data-v-536f45d8 {
  font-size: 32rpx;
  color: #333;
  flex: 1;
}
.category-item .item-arrow.data-v-536f45d8 {
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.category-item .item-arrow .arrow-icon.data-v-536f45d8 {
  font-size: 24rpx;
  color: #999;
  font-weight: bold;
}
.hint-text.data-v-536f45d8 {
  text-align: center;
  margin-bottom: 30rpx;
}
.hint-text .hint.data-v-536f45d8 {
  font-size: 26rpx;
  color: #999;
}
.action-buttons.data-v-536f45d8 {
  display: flex;
  gap: 20rpx;
}
.action-buttons .btn.data-v-536f45d8 {
  flex: 1;
  height: 80rpx;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.action-buttons .btn.btn-back.data-v-536f45d8 {
  background-color: #F5F5F5;
  color: #666;
}
.action-buttons .btn.btn-back.data-v-536f45d8:active {
  background-color: #E5E5E5;
}
.action-buttons .btn.btn-next.data-v-536f45d8 {
  background-color: #FFD101;
  color: #333;
}
.action-buttons .btn.btn-next.data-v-536f45d8:active {
  background-color: #E6BC00;
}
