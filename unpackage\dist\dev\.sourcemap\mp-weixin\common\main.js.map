{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/KingToyo/app_tixiaomeng/App.vue?a554", "uni-app:///App.vue", "webpack:///E:/KingToyo/app_tixiaomeng/App.vue?be2e", "webpack:///E:/KingToyo/app_tixiaomeng/App.vue?842d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "prototype", "$store", "store", "$storage", "storage", "config", "productionTip", "App", "mpType", "use", "uView", "app", "$mount", "onLaunch", "console", "onShow", "onHide", "methods", "getToken", "getMToken", "getPhone", "getUserId", "getMerchant", "getSessionKey", "getOpenId", "getInvitationCode", "getScreenHeight"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAG3D;AACA;AACA;AACA;AACA;AAAsC;AAAA;AARtC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAQ1DC,YAAG,CAACC,SAAS,CAACC,MAAM,GAAGC,cAAK;AAC5BH,YAAG,CAACC,SAAS,CAACG,QAAQ,GAAGC,gBAAO;AAChCL,YAAG,CAACM,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClBT,YAAG,CAACU,GAAG,CAACC,gBAAK,CAAC;AACd,IAAMC,GAAG,GAAG,IAAIZ,YAAG;EAClBG,KAAK,EAALA;AAAK,GACDK,YAAG,EACN;AACF,UAAAI,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACnBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAwqB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCC5rB;EACAC;IACAC;EACA;EACAC;IACAD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAE;IACAF;EACA;EACAG;IACA;IACAC;MACA;QACA;MACA;QACAJ;MACA;IACA;IACA;IACAK;MACA;QACA;MACA;QACAL;MACA;IACA;IACA;IACAM;MACA;QACA;MACA;QACAN;MACA;IACA;IACA;IACAO;MACA;QACA;MACA;QACAP;MACA;IACA;IACA;IACAQ;MACA;QACA;MACA;QACAR;MACA;IACA;IACA;IACAS;MACA;QACA;MACA;QACAT;MACA;IACA;IACA;IACAU;MACA;QACA;MACA;QACAV;MACA;IACA;IACA;IACAW;MACA;QACA;MACA;QACAX;MACA;IACA;IACA;IACAY;MACA;QACA;MACA;QACAZ;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAA+wC,CAAgB,4uCAAG,EAAC,C;;;;;;;;;;;ACAnyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\r\n\r\n\r\nimport Vue from 'vue'\r\nimport uView from '@/uni_modules/uview-ui'\r\nimport './uni.promisify.adaptor'\r\nimport store from './store'\r\nimport storage from \"@/config/storage\"\r\nVue.prototype.$store = store\r\nVue.prototype.$storage = storage\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nVue.use(uView)\r\nconst app = new Vue({\r\n\tstore,\r\n  ...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t\t// 获取token缓存到Vuex中\r\n\t\t\tthis.getToken()\r\n\t\t\t// 获取mToken缓存到Vuex中\r\n\t\t\tthis.getMToken()\r\n\t\t\t// 获取phone缓存到Vuex中\r\n\t\t\tthis.getPhone()\r\n\t\t\t// 获取userId缓存到Vuex中\r\n\t\t\tthis.getUserId()\r\n\t\t\t// 获取merchant缓存到Vuex中\r\n\t\t\tthis.getMerchant()\r\n\t\t\t// 获取sessionKey缓存到Vuex中\r\n\t\t\tthis.getSessionKey()\r\n\t\t\t// 获取openId缓存到Vuex中\r\n\t\t\tthis.getOpenId()\r\n\t\t\t// 获取invitationCode缓存到Vuex中\r\n\t\t\tthis.getInvitationCode()\r\n\t\t\t// 获取手机设备信息 保存Vuex中\r\n\t\t\tthis.getScreenHeight()\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取当前账号的token信息\r\n\t\t\tgetToken() {\r\n\t\t\t  try {\r\n\t\t\t    this.$store.dispatch(\"GetToken\")\r\n\t\t\t  } catch (err) {\r\n\t\t\t    console.log(\"getStorageSync token err\")\r\n\t\t\t  }\r\n\t\t\t},\r\n\t\t\t// 获取当前账号的mToken信息\r\n\t\t\tgetMToken() {\r\n\t\t\t  try {\r\n\t\t\t    this.$store.dispatch(\"GetMToken\")\r\n\t\t\t  } catch (err) {\r\n\t\t\t    console.log(\"getStorageSync token err\")\r\n\t\t\t  }\r\n\t\t\t},\r\n\t\t\t// 获取当前账号的phone信息\r\n\t\t\tgetPhone() {\r\n\t\t\t  try {\r\n\t\t\t    this.$store.dispatch(\"GetPhone\")\r\n\t\t\t  } catch (err) {\r\n\t\t\t    console.log(\"getStorageSync token err\")\r\n\t\t\t  }\r\n\t\t\t},\r\n\t\t\t// 获取当前账号的userId信息\r\n\t\t\tgetUserId() {\r\n\t\t\t  try {\r\n\t\t\t    this.$store.dispatch(\"GetUserId\")\r\n\t\t\t  } catch (err) {\r\n\t\t\t    console.log(\"getStorageSync userId err\")\r\n\t\t\t  }\r\n\t\t\t},\r\n\t\t\t// 获取当前账号的merchant信息\r\n\t\t\tgetMerchant() {\r\n\t\t\t  try {\r\n\t\t\t    this.$store.dispatch(\"GetMerchant\")\r\n\t\t\t  } catch (err) {\r\n\t\t\t    console.log(\"getStorageSync userId err\")\r\n\t\t\t  }\r\n\t\t\t},\r\n\t\t\t// 获取当前账号的sessionKey信息\r\n\t\t\tgetSessionKey() {\r\n\t\t\t  try {\r\n\t\t\t    this.$store.dispatch(\"GetSessionKey\")\r\n\t\t\t  } catch (err) {\r\n\t\t\t    console.log(\"getStorageSync sessionKey err\")\r\n\t\t\t  }\r\n\t\t\t},\r\n\t\t\t// 获取当前账号的openId信息\r\n\t\t\tgetOpenId() {\r\n\t\t\t  try {\r\n\t\t\t    this.$store.dispatch(\"GetOpenId\")\r\n\t\t\t  } catch (err) {\r\n\t\t\t    console.log(\"getStorageSync openId err\")\r\n\t\t\t  }\r\n\t\t\t},\r\n\t\t\t// 获取当前账号的invitationCode信息\r\n\t\t\tgetInvitationCode() {\r\n\t\t\t  try {\r\n\t\t\t    this.$store.dispatch(\"GetInvitationCode\")\r\n\t\t\t  } catch (err) {\r\n\t\t\t    console.log(\"getStorageSync invitationCode err\")\r\n\t\t\t  }\r\n\t\t\t},\r\n\t\t\t// 获取手机系统信息保存在VUEX中\r\n\t\t\tgetScreenHeight() {\r\n\t\t\t\ttry {\r\n\t\t\t\t  this.$store.dispatch(\"GetAppSystemInfoSync\")\r\n\t\t\t\t} catch (err) {\r\n\t\t\t\t  console.log(\"getStorageSync appSystemInfoSync err\")\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import \"@/uni_modules/uview-ui/index.scss\";\r\n\tview {\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749956144392\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}