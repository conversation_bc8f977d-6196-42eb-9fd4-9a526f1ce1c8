<view class="wrong-content data-v-38014372"><u-navbar vue-id="c82628c2-1" placeholder="{{true}}" bgColor="#FFD101" autoBack="{{true}}" class="data-v-38014372" bind:__l="__l" vue-slots="{{['left']}}"><view class="u-nav-slot data-v-38014372" slot="left"><image class="arrow data-v-38014372" src="/static/images/arrow-left.png"></image>L3 错题本</view></u-navbar><u-gap vue-id="c82628c2-2" height="50rpx" bgColor="#FFD101" class="data-v-38014372" bind:__l="__l"></u-gap><view class="wrong-list data-v-38014372"><text class="wrong-title data-v-38014372">{{"共"+$root.g0+"题"}}</text><block wx:for="{{wrongList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="wrong-li data-v-38014372"><text class="content u-line-2 data-v-38014372">{{item.content}}</text></view></block><block wx:if="{{$root.g1>0}}"><u-loadmore vue-id="c82628c2-3" line="{{true}}" status="{{loadmore}}" class="data-v-38014372" bind:__l="__l"></u-loadmore></block></view><u-empty vue-id="c82628c2-4" show="{{isData}}" text="暂无错题本" marginTop="100rpx" class="data-v-38014372" bind:__l="__l"></u-empty><u-safe-bottom vue-id="c82628c2-5" class="data-v-38014372" bind:__l="__l"></u-safe-bottom><view class="position-bottom data-v-38014372"><view class="button-list data-v-38014372"><button data-event-opts="{{[['tap',[['onPractise',[1]]]]]}}" class="{{['u-reset-button','button','data-v-38014372',($root.g2>0)?'button-s':'']}}" bindtap="__e"><text class="text data-v-38014372">清空</text></button><button data-event-opts="{{[['tap',[['onPractise',[2]]]]]}}" class="{{['u-reset-button','button','data-v-38014372',($root.g3>0)?'button-s':'']}}" bindtap="__e"><text class="text data-v-38014372">练习</text></button></view><u-safe-bottom vue-id="c82628c2-6" class="data-v-38014372" bind:__l="__l"></u-safe-bottom></view></view>