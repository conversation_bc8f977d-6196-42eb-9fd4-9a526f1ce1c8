{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/list.vue?84bc", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/list.vue?4707", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/list.vue?e713", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/list.vue?38aa", "uni-app:///pages/index/list.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/list.vue?193a", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/list.vue?4b9c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "filters", "methods", "onAnswer", "uni", "url", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onReachBottom", "onPullDownRefresh", "onBackPress", "onPageScroll", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAyqB,CAAgB,8rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkH7rB;EACAC;EACAC;IAAA,QAEA;EAAA;EACAC;EACAC;IACA;IACAC;MACA;MACAC;QACAC;MACA;IACA;EACA;EACA;EACAC,kCAEA;EACA;EACAC;EACA;EACAC,2BAEA;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC3JA;AAAA;AAAA;AAAA;AAAwyC,CAAgB,qwCAAG,EAAC,C;;;;;;;;;;;ACA5zC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=fc532118&scoped=true&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=fc532118&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fc532118\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/list.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=template&id=fc532118&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-navbar/u-navbar\" */ \"@/uni_modules/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-image/u-image\" */ \"@/uni_modules/uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"list-content\">\r\n\t\t<u-navbar :placeholder=\"true\" bgColor=\"#FFD101\" :autoBack=\"true\">\r\n\t\t\t<view class=\"u-nav-slot\" slot=\"left\"><image class=\"arrow\" src=\"@/static/images/arrow-left.png\" /> L系列</view>\r\n\t\t</u-navbar>\r\n\t\t<view class=\"question-list\">\r\n\t\t\t<text class=\"list-title\">与豆伴匠配套题库 - L系列</text>\r\n\t\t\t<view class=\"question-li\">\r\n\t\t\t\t<view class=\"left-item\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"130rpx\" height=\"130rpx\" radius=\"47rpx\" src=\"@/static/images/1.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t\t<text class=\"title\">L1</text>\r\n\t\t\t\t\t\t<view class=\"schedule\">\r\n\t\t\t\t\t\t\t<text class=\"label\">题数：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">0</text>\r\n\t\t\t\t\t\t\t<text class=\"label margin\">/</text>\r\n\t\t\t\t\t\t\t<text class=\"label\">2359</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"u-reset-button answer\" @click=\"onAnswer\">准备答题</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"question-li\">\r\n\t\t\t\t<view class=\"left-item\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"130rpx\" height=\"130rpx\" radius=\"47rpx\" src=\"@/static/images/2.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t\t<text class=\"title\">L2</text>\r\n\t\t\t\t\t\t<view class=\"schedule\">\r\n\t\t\t\t\t\t\t<text class=\"label\">题数：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">0</text>\r\n\t\t\t\t\t\t\t<text class=\"label margin\">/</text>\r\n\t\t\t\t\t\t\t<text class=\"label\">2359</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"u-reset-button answer\">准备答题</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"question-li\">\r\n\t\t\t\t<view class=\"left-item\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"130rpx\" height=\"130rpx\" radius=\"47rpx\" src=\"@/static/images/3.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t\t<text class=\"title\">L3</text>\r\n\t\t\t\t\t\t<view class=\"schedule\">\r\n\t\t\t\t\t\t\t<text class=\"label\">题数：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">200</text>\r\n\t\t\t\t\t\t\t<text class=\"label margin\">/</text>\r\n\t\t\t\t\t\t\t<text class=\"label\">5432</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"u-reset-button answer\">准备答题</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"question-li\">\r\n\t\t\t\t<view class=\"left-item\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"130rpx\" height=\"130rpx\" radius=\"47rpx\" src=\"@/static/images/4.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t\t<text class=\"title\">L4</text>\r\n\t\t\t\t\t\t<view class=\"schedule\">\r\n\t\t\t\t\t\t\t<text class=\"label\">题数：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">0</text>\r\n\t\t\t\t\t\t\t<text class=\"label margin\">/</text>\r\n\t\t\t\t\t\t\t<text class=\"label\">12359</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"u-reset-button answer\">准备答题</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"question-li\">\r\n\t\t\t\t<view class=\"left-item\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"130rpx\" height=\"130rpx\" radius=\"47rpx\" src=\"@/static/images/5.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t\t<text class=\"title\">L5</text>\r\n\t\t\t\t\t\t<view class=\"schedule\">\r\n\t\t\t\t\t\t\t<text class=\"label\">题数：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">33</text>\r\n\t\t\t\t\t\t\t<text class=\"label margin\">/</text>\r\n\t\t\t\t\t\t\t<text class=\"label\">5359</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"u-reset-button answer\">准备答题</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"question-li\">\r\n\t\t\t\t<view class=\"left-item\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"130rpx\" height=\"130rpx\" radius=\"47rpx\" src=\"@/static/images/6.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t\t<text class=\"title\">L6</text>\r\n\t\t\t\t\t\t<view class=\"schedule\">\r\n\t\t\t\t\t\t\t<text class=\"label\">题数：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">0</text>\r\n\t\t\t\t\t\t\t<text class=\"label margin\">/</text>\r\n\t\t\t\t\t\t\t<text class=\"label\">2359</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"u-reset-button not-activation\">待激活</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tcomponents: {},\r\n\t\tdata: () => ({\r\n\t\t\t\r\n\t\t}),\r\n\t\tfilters: {},\r\n\t\tmethods: {\r\n\t\t\t// 答题\r\n\t\t\tonAnswer() {\r\n\t\t\t\t// 跳转至答题页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/index/answer'\n\t\t\t\t});\n\t\t\t},\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面加载\r\n\t\tonLoad(options) {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面初次渲染完成\r\n\t\tonReady() {},\r\n\t\t// 页面周期函数--监听页面显示(not-nvue)\r\n\t\tonShow() {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面隐藏\r\n\t\tonHide() {},\r\n\t\t// 页面周期函数--监听页面卸载\r\n\t\tonUnload() {},\r\n\t\t// 页面处理函数--监听用户上拉触底\r\n\t\tonReachBottom() {},\r\n\t\t// 页面处理函数--监听用户下拉动作\r\n\t\tonPullDownRefresh() {},\r\n\t\t// 页面周期函数--监听页面返回\r\n\t\tonBackPress() {},\r\n\t\t// 页面处理函数--监听页面滚动(not-nvue)\r\n\t\tonPageScroll(e) {},\r\n\t\t// 页面处理函数--用户点击右上角分享好友\r\n\t\tonShareAppMessage(options) {},\r\n\t\t// 页面处理函数--用户点击右上角分享朋友圈\r\n\t\tonShareTimeline(options) {}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.list-content {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 100vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbackground-image: linear-gradient(to bottom, #FFD101 10%, #FFF 50%);\r\n\t\t.u-nav-slot {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #000000;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t.arrow {\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.question-list {\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tpadding: 55rpx 60rpx;\r\n\t\t\tborder-radius: 43rpx 43rpx 0 0;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\tmargin-top: 15rpx;\r\n\t\t\t.list-title {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tmargin-bottom: 35rpx;\r\n\t\t\t}\r\n\t\t\t.question-li {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tpadding: 16rpx 0;\r\n\t\t\t\t.left-item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t.image {\r\n\t\t\t\t\t\twidth: 130rpx;\r\n\t\t\t\t\t\theeight: 130rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.title-schedule {\r\n\t\t\t\t\t\theight: 130rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\tmargin-left: 29rpx;\r\n\t\t\t\t\t\t.title {\r\n\t\t\t\t\t\t\tfont-size: 31rpx;\r\n\t\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\t\tcolor: #464646;\r\n\t\t\t\t\t\t\tmargin-bottom: 22rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.schedule {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t.label {\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\tcolor: #464646;\r\n\t\t\t\t\t\t\t\t&.margin {\r\n\t\t\t\t\t\t\t\t\tmargin: 0 5rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t.value {\r\n\t\t\t\t\t\t\t\tcolor: #F4621A;\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.answer {\r\n\t\t\t\t\twidth: 182rpx;\r\n\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #000000;\r\n\t\t\t\t\tbackground-color: #FFD101;\r\n\t\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\ttransition: all 100ms cubic-bezier(.36, .66, .04, 1);\r\n\t\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\tbackground-color: #ebc000;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.not-activation {\r\n\t\t\t\t\twidth: 182rpx;\r\n\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #000000;\r\n\t\t\t\t\tbackground-color: #D6D6D6;\r\n\t\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\ttransition: all 100ms cubic-bezier(.36, .66, .04, 1);\r\n\t\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(214, 214, 214, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(214, 214, 214, 0.2);\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\tbackground-color: #c3c3c3;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&id=fc532118&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&id=fc532118&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749956143971\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}