<view class="success-content data-v-0caffd1d"><view class="header data-v-0caffd1d"><text class="title data-v-0caffd1d">太棒了!</text></view><view class="score-section data-v-0caffd1d"><view class="progress-container data-v-0caffd1d"><view class="progress-circle data-v-0caffd1d"><view class="progress-bg data-v-0caffd1d"></view><view class="progress-bar data-v-0caffd1d" style="{{'transform:'+('rotate('+progressRotation+'deg)')+';'}}"></view><view class="progress-dot data-v-0caffd1d" style="{{'transform:'+('rotate('+progressRotation+'deg)')+';'}}"></view><view class="progress-center data-v-0caffd1d"><text class="score-text data-v-0caffd1d">{{animatedScore+"%"}}</text><text class="score-label data-v-0caffd1d">正确率</text></view></view></view><view class="stats-container data-v-0caffd1d"><view class="stats-item data-v-0caffd1d"><text class="stats-number data-v-0caffd1d">{{answeredCount}}</text><text class="stats-label data-v-0caffd1d">答题数</text></view><view class="stats-item data-v-0caffd1d"><text class="stats-number correct data-v-0caffd1d">{{correctCount}}</text><text class="stats-label data-v-0caffd1d">答对</text></view><view class="stats-item data-v-0caffd1d"><text class="stats-number wrong data-v-0caffd1d">{{wrongCount}}</text><text class="stats-label data-v-0caffd1d">答错</text></view></view></view><view class="action-buttons data-v-0caffd1d"><button data-event-opts="{{[['tap',[['checkOrder',['$event']]]]]}}" class="btn btn-primary data-v-0caffd1d" bindtap="__e">进入错题本</button><button data-event-opts="{{[['tap',[['returnHome',['$event']]]]]}}" class="btn btn-secondary data-v-0caffd1d" bindtap="__e">返回</button></view></view>