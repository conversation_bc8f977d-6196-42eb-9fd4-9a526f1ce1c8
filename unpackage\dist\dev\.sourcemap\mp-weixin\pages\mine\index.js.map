{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/KingToyo/app_tixiaomeng/pages/mine/index.vue?5902", "webpack:///E:/KingToyo/app_tixiaomeng/pages/mine/index.vue?f510", "webpack:///E:/KingToyo/app_tixiaomeng/pages/mine/index.vue?d27d", "webpack:///E:/KingToyo/app_tixiaomeng/pages/mine/index.vue?804a", "uni-app:///pages/mine/index.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/mine/index.vue?b6ed", "webpack:///E:/KingToyo/app_tixiaomeng/pages/mine/index.vue?ea7d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "quickLogin", "data", "is<PERSON>ogin", "loginShow", "isOperation", "nickname", "loading", "phone", "couponCount", "orderCount", "methods", "onOperate", "uni", "url", "inputBox", "title", "editable", "placeholderText", "success", "icon", "that", "console", "loginSuccess", "<PERSON><PERSON><PERSON>in", "logOut", "content", "changeLogin", "fail", "doFindByCode", "code", "res", "getFindByMobile", "mobile", "filteredArray", "getUserInfo", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onReachBottom", "onPullDownRefresh", "onBackPress", "onPageScroll", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC8F9rB;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EAAA;;EACAC;IACA;IACAC;MACA;QACA;UACA;UACAC;YACAC;UACA;QACA;UACA;UACAD;YACAC;UACA;QACA;UACA;UACAD;YACAC;UACA;QACA;UACA;QAAA,CAEA;UACA;UACAD;YACAC;UACA;QACA;UACA;UACA;UACA;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAF;QACAG;QACAC;QACAC;QACAC;UACA;YACA;cACAN;gBACAO;gBACAJ;cACA;cACA;YACA;cACAK;cACAR;gBACAC;cACA;YACA;UACA;YACAQ;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACAZ;QACAG;QACAU;QACAP;UACA;YACAE;YACAA;YACAR;cACAO;cACAJ;YACA;UACA;QACA;MACA;IACA;IACA;IACAW;MACA;MACAd;QACAM;UACAE;UACAA;QACA;QAAAO;UACAf;QACA;MACA;IACA;IACA;IACAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAR;gBAAA;gBAAA,OACA;kBAAAS;gBAAA;cAAA;gBAAA;gBAAAA;gBAAA5B;gBACA;kBACA6B;kBACAV;kBACAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBAAAC;gBAAA;cAAA;gBAAA;gBAAAH;gBAAA5B;gBACA;kBACAgC;oBAAA;kBAAA,IACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAZ;gBACAT;kBACAO;kBACAJ;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAAL;gBAAA5B;gBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAoB;gBACAT;kBACAO;kBACAJ;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACA;EACAoB;IACA;MACAd;IACA;MACA;IACA;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;MACA;IACA;EACA;EACA;EACAe;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC5TA;AAAA;AAAA;AAAA;AAAyyC,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACA7zC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4bd6864f&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4bd6864f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4bd6864f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=4bd6864f&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-navbar/u-navbar\" */ \"@/uni_modules/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uBadge: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-badge/u-badge\" */ \"@/uni_modules/uview-ui/components/u-badge/u-badge.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-image/u-image\" */ \"@/uni_modules/uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"mine-content\">\r\n\t\t<u-navbar placeholder fixed bgColor=\"transparent\" leftIconColor=\"transparent\" />\r\n\t\t<view class=\"avatar-nickname\" v-if=\"isLogin\">\r\n\t\t\t<view class=\"left-info\">\r\n\t\t\t\t<image class=\"avatar\" src=\"https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1912338122954321921.png\" />\r\n\t\t\t\t<text class=\"nickname\">{{ nickname }}</text>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"u-reset-button login\" @click=\"logOut\">退出登录</button>\r\n\t\t</view>\r\n\t\t<view class=\"avatar-nickname\" v-else>\r\n\t\t\t<view class=\"left-info\">\r\n\t\t\t\t<image class=\"avatar\" src=\"@/static/images/avatar.png\" />\r\n\t\t\t\t<text class=\"nickname\">未登录</text>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"u-reset-button login\" @click=\"immediatelyLogin\">立即登录</button>\r\n\t\t</view>\r\n\t\t<view class=\"order-center\">\r\n\t\t\t<view class=\"item-li\" @click=\"onOperate(1)\">\r\n\t\t\t\t<image class=\"image\" src=\"https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1912399270386937857.png\" />\r\n\t\t\t\t<text class=\"text\">我的订单</text>\r\n\t\t\t\t<u-badge bgColor=\"#FF6233\" :absolute=\"true\" :value=\"orderCount\" :offset=\"[8, 32]\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item-li\" @click=\"onOperate(2)\">\r\n\t\t\t\t<image class=\"image\" src=\"https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1912399348661039106.png\" />\r\n\t\t\t\t<text class=\"text\">优惠券</text>\r\n\t\t\t\t<u-badge bgColor=\"#FF6233\" :absolute=\"true\" :value=\"couponCount\" :offset=\"[8, 32]\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item-li\" @click=\"onOperate(3)\">\r\n\t\t\t\t<image class=\"image\" src=\"https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1912399419150512130.png\" />\r\n\t\t\t\t<text class=\"text\">邀请好友</text>\r\n\t\t\t\t<image class=\"ad\" src=\"https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1912403137828827138.png\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"invite-cont\">\r\n\t\t\t<u-image\r\n\t\t\t\tsrc=\"https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1912405741023604737.png\"\r\n\t\t\t\twidth=\"710rpx\"\r\n\t\t\t\theight=\"118rpx\"\r\n\t\t\t\tradius=\"17rpx\"\r\n\t\t\t\t@click=\"onOperate(3)\"\r\n\t\t\t></u-image>\r\n\t\t</view>\r\n\t\t<view class=\"operate-item\">\r\n\t\t\t<view class=\"item-li\" @click=\"onOperate(4)\">\r\n\t\t\t\t<view class=\"left-info\">\r\n\t\t\t\t\t<image class=\"image\" src=\"https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1912407816402972673.png\" />\r\n\t\t\t\t\t<text class=\"label\">客服</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" size=\"23rpx\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item-li\" @click=\"onOperate(5)\">\r\n\t\t\t\t<view class=\"left-info\">\r\n\t\t\t\t\t<image class=\"image\" src=\"https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1912409775931469826.png\" />\r\n\t\t\t\t\t<text class=\"label\">意见反馈</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" size=\"23rpx\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item-li\" @click=\"onOperate(6)\" v-if=\"isOperation\">\r\n\t\t\t<!-- <view class=\"item-li\" @click=\"onOperate(3)\" > -->\r\n\t\t\t\t<view class=\"left-info\">\r\n\t\t\t\t\t<image class=\"image\" src=\"https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1912409833955471361.png\" />\r\n\t\t\t\t\t<text class=\"label\">运营中心</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" size=\"23rpx\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item-li\" @click=\"onOperate(7)\">\r\n\t\t\t\t<view class=\"left-info\">\r\n\t\t\t\t\t<image class=\"image\" src=\"https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/icon/1912407604347351041.png\" />\r\n\t\t\t\t\t<text class=\"label\">补货配送</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" size=\"23rpx\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 授权登录 -->\r\n\t\t<u-popup :show=\"loginShow\" mode=\"center\" round=\"20rpx\">\r\n\t\t\t<view class=\"login-centent\">\r\n\t\t\t\t<text class=\"login-title\">隐私协议政策提示</text>\r\n\t\t\t\t<view class=\"login-text\">\r\n\t\t\t\t\t<text class=\"text\">感谢您使用鲜吧啦小程序，当您点击同意并开始使用本产品服务时，即表示您理解并同意</text>\r\n\t\t\t\t\t<text class=\"special\" @click=\"openPrivacyContract\">《鲜吧啦小程序隐私保护指引》</text>\r\n\t\t\t\t\t<text class=\"text\">的条款信息，该条款将对您产生法律保护与法律约束力，如您拒绝，则您无法享受该条款之中的权益。</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"login-button\">\r\n\t\t\t\t\t<button type=\"default\" class=\"u-reset-button cancel-button\" @click=\"changeLogin\">不同意</button>\r\n\t\t\t\t\t<button type=\"default\" class=\"u-reset-button agree-button\" @click=\"changeLogin\">同意并继续</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t\t<quick-login ref=\"login\" @loginSuccess=\"loginSuccess\" />\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport quickLogin from '@/components/quickLogin.vue'\r\n\timport { findByMobile, getUser } from '@/api/mine'\r\n\timport { wxGetOpenId } from '@/api/login'\r\n \texport default {\r\n\t\tcomponents: {\r\n\t\t\tquickLogin\r\n\t\t},\r\n\t\tdata: () => ({\r\n\t\t\tisLogin: false, // 是否已登录\r\n\t\t\tloginShow: false, // 登录弹窗状态\r\n\t\t\tisOperation: true, // 是否显示运营\r\n\t\t\tnickname: '未登录', // 登录昵称\r\n\t\t\tloading: true, // 加载状态\r\n\t\t\tphone: '', // 手机号\r\n\t\t\tcouponCount: 0, // 优惠券数量\r\n\t\t\torderCount: 0, // 订单数量\r\n\t\t}),\r\n\t\tmethods: {\r\n\t\t\t// 订单操作\r\n\t\t\tonOperate(e) {\r\n\t\t\t\tif (this.$store.state.user.token) {\r\n\t\t\t\t\tif (e == 1) {\r\n\t\t\t\t\t\t// 我的订单\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/mine/order/list'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else if (e == 2) {\r\n\t\t\t\t\t\t// 我的优惠券\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/mine/coupon'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else if (e == 3) {\r\n\t\t\t\t\t\t// 邀请好友\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/mine/invite'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else if (e == 4) {\r\n\t\t\t\t\t\t// 客服\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t} else if (e == 5) {\r\n\t\t\t\t\t\t// 意见反馈\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/pages/mine/feedback/index'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else if (e == 6) {\r\n\t\t\t\t\t\t// 运营中心\r\n\t\t\t\t\t\t// uni.reLaunch({\r\n\t\t\t\t\t\t// \turl: '/pages/store/index'\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t\tthis.inputBox()\r\n\t\t\t\t\t} else if (e == 7) {\r\n\t\t\t\t\t\t// 补货送货\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: `/pages/mine/restocking`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 立即登录\r\n\t\t\t\t\tthis.immediatelyLogin()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 重新输入框\r\n\t\t\tinputBox() {\r\n\t\t\t\tlet that = this\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\teditable: true,\r\n\t\t\t\t\tplaceholderText: '请输入商户账号',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tif (!uni.$u.test.mobile(res.content)) {\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\t\ttitle: '请输入正确的商户账号'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\treturn false\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthat.$store.dispatch(\"SetPhone\", res.content)\r\n\t\t\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/store/index'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\n\t\t\t},\r\n\t\t\t// 登录成功\r\n\t\t\tloginSuccess(e) {\r\n\t\t\t\tthis.nickname = e.phone\r\n\t\t\t\tthis.phone = e.phone\r\n\t\t\t\tthis.isLogin = true\r\n\t\t\t\t// 获取商户信息\r\n\t\t\t\tthis.getFindByMobile()\r\n\t\t\t\t// 获取用户信息\r\n\t\t\t\tthis.getUserInfo()\r\n\t\t\t},\r\n\t\t\t// 立即登录\r\n\t\t\timmediatelyLogin() {\r\n\t\t\t\tthis.$refs.login.show()\r\n\t\t\t},\r\n\t\t\t// 退出登录\r\n\t\t\tlogOut() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t  title: \"提示\",\r\n\t\t\t\t  content: \"确认要退出登录吗？\",\r\n\t\t\t\t  success: function (res) {\r\n\t\t\t\t    if (res.confirm) {\r\n\t\t\t\t      that.$store.dispatch(\"SetToken\", \"\")\r\n\t\t\t\t\t\t\tthat.isLogin = false\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\ttitle: '退出成功'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t    }\r\n\t\t\t\t  }\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 授权登录\r\n\t\t\tchangeLogin() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tsuccess: function (loginRes) {\r\n\t\t\t\t\t\tthat.doFindByCode(loginRes.code)\r\n\t\t\t\t\t\tthat.loginShow = false\r\n\t\t\t\t\t}, fail(loginError) {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 上传Code\r\n\t\t\tasync doFindByCode(e) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet { code, data } = await wxGetOpenId({ code: e })\r\n\t\t\t\tif (code == '00000') {\r\n\t\t\t\t\tlet res = JSON.parse(data)\r\n\t\t\t\t\tthat.$store.dispatch(\"SetOpenId\", res.openid)\r\n\t\t\t\t\tthat.$store.dispatch(\"SetSessionKey\", res.sessionKey)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 获取商户信息\r\n\t\t\tasync getFindByMobile() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst { code, data } = await findByMobile({ mobile: this.phone })\r\n\t\t\t\t\tif (code == '00000') {\r\n\t\t\t\t\t\tconst filteredArray = data.filter(item => item.userType == \"1\")\r\n\t\t\t\t\t\t// if (filteredArray.length > 0) {\r\n\t\t\t\t\t\t// \tthis.isOperation = true\r\n\t\t\t\t\t\t// } else {\r\n\t\t\t\t\t\t// \tthis.isOperation = false\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取商户信息失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '获取商户信息失败'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 获取用户信息\r\n\t\t\tasync getUserInfo() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst { code, data } = await getUser({})\r\n\t\t\t\t\tif (code == '00000') {\r\n\t\t\t\t\t\tif (data.orderCount) {\r\n\t\t\t\t\t\t\tthis.orderCount = data.orderCount\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (data.couponCount) {\r\n\t\t\t\t\t\t\tthis.couponCount = data.couponCount\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取用户信息失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '获取用户信息失败'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面加载\r\n\t\tonLoad(options) {\r\n\t\t\tif (this.$store.state.user.openId) {\r\n\t\t\t\tconsole.log('已授权openId')\r\n\t\t\t} else {\r\n\t\t\t\tthis.loginShow = true\r\n\t\t\t}\r\n\t\t\tif (this.$store.state.user.token) {\r\n\t\t\t\tlet phone = this.$store.state.user.phone\r\n\t\t\t\tthis.nickname = phone\r\n\t\t\t\tthis.phone = phone\r\n\t\t\t\t// 获取商户信息\r\n\t\t\t\tthis.getFindByMobile()\r\n\t\t\t\t// 获取用户信息\r\n\t\t\t\tthis.getUserInfo()\r\n\t\t\t\tthis.isLogin = true\r\n\t\t\t} else {\r\n\t\t\t\tthis.isLogin = false\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面初次渲染完成\r\n\t\tonReady() {},\r\n\t\t// 页面周期函数--监听页面显示(not-nvue)\r\n\t\tonShow() {},\r\n\t\t// 页面周期函数--监听页面隐藏\r\n\t\tonHide() {},\r\n\t\t// 页面周期函数--监听页面卸载\r\n\t\tonUnload() {},\r\n\t\t// 页面处理函数--监听用户上拉触底\r\n\t\tonReachBottom() {},\r\n\t\t// 页面处理函数--监听用户下拉动作\r\n\t\tonPullDownRefresh() {},\r\n\t\t// 页面周期函数--监听页面返回\r\n\t\tonBackPress() {},\r\n\t\t// 页面处理函数--监听页面滚动(not-nvue)\r\n\t\tonPageScroll(e) {},\r\n\t\t// 页面处理函数--用户点击右上角分享好友\r\n\t\tonShareAppMessage(options) {},\r\n\t\t// 页面处理函数--用户点击右上角分享朋友圈\r\n\t\tonShareTimeline(options) {}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.mine-content {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 100vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tbackground-image: linear-gradient(0deg, #F5F5F5 60%, #FC9701 100%);\r\n\t\tpadding: 25rpx 20rpx 0;\r\n\t\t.avatar-nickname {\r\n\t\t\twidth: 710rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\t.left-info {\r\n\t\t\t\twidth: 520rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t.avatar {\r\n\t\t\t\t\twidth: 100rpx;\r\n\t\t\t\t\theight: 100rpx;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.nickname {\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.login {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tcolor: #FFF;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\ttransition: all 100ms cubic-bezier(.36,.66,.04,1);\r\n\t\t\t}\r\n\t\t}\r\n\t\t.order-center {\r\n\t\t\twidth: 710rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tborder-radius: 14rpx;\r\n\t\t\tbackground-color: #FFF;\r\n\t\t\tpadding: 24rpx;\r\n\t\t\tmargin: 28rpx 0 20rpx;\r\n\t\t\t.item-li {\r\n\t\t\t\twidth: 220rpx;\r\n\t\t\t\theight: 150rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\ttransition: all 100ms cubic-bezier(.36,.66,.04,1);\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t&:active {\r\n\t\t\t\t\tbackground-color: #F9F9F9;\r\n\t\t\t\t}\r\n\t\t\t\t.image {\r\n\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\tmargin-bottom: 15rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.text {\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #5B5B5B;\r\n\t\t\t\t}\r\n\t\t\t\t.ad {\r\n\t\t\t\t\twidth: 97rpx;\r\n\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 10rpx;\r\n\t\t\t\t\tright: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.invite-cont {\r\n\t\t\twidth: 710rpx;\r\n\t\t\theight: 118rpx;\r\n\t\t}\r\n\t\t.operate-item {\r\n\t\t\twidth: 710rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 10rpx 20rpx;\r\n\t\t\tbackground-color: #FFF;\r\n\t\t\tborder-radius: 14rpx;\r\n\t\t\tmargin-top: 21rpx;\r\n\t\t\t\r\n\t\t\t.item-li {\r\n\t\t\t\twidth: 670rpx;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\ttransition: all 100ms cubic-bezier(.36,.66,.04,1);\r\n\t\t\t\tborder-bottom: 2rpx solid #E9E9E9;\r\n\t\t\t\t&:active {\r\n\t\t\t\t\tbackground-color: #F9F9F9;\r\n\t\t\t\t}\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: 0;\r\n\t\t\t\t}\r\n\t\t\t\t.left-info {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t.label {\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tfont-size: 23rpx;\r\n\t\t\t\t\t\tcolor: #5B5B5B;\r\n\t\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.image {\r\n\t\t\t\t\t\twidth: 36rpx;\r\n\t\t\t\t\t\theight: 36rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t\t.login-centent {\r\n\t\t\twidth: 640rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\t.login-title {\r\n\t\t\t\twidth: 640rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #303133;\r\n\t\t\t\tmargin: 60rpx 0 40rpx;\r\n\t\t\t}\r\n\t\t\t.login-text {\r\n\t\t\t\twidth: 640rpx;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #606266;\r\n\t\t\t\tpadding: 0 40rpx;\r\n\t\t\t\ttext-align: justify;\r\n\t\t\t\tline-height: 1.5;\r\n\t\t\t\t.special {\r\n\t\t\t\t\tcolor: #2979ff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.login-button {\r\n\t\t\t\twidth: 640rpx;\r\n\t\t\t\tpadding: 0 40rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tmargin: 60rpx 0rpx 40rpx;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t.cancel-button {\r\n\t\t\t\t\twidth: 260rpx;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\tbackground-color: #FFF;\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\tborder: 2rpx solid #82848a;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #82848a;\r\n\t\t\t\t\ttransition: all 100ms cubic-bezier(.36,.66,.04,1);\r\n\t\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(130,132,138,0.1), 0rpx 6rpx 16rpx 0rpx rgba(130,132,138,0.1);\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\tbackground-color: #F4F4F4;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.agree-button {\r\n\t\t\t\t\twidth: 260rpx;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\tbackground-color: #FF8519;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #FFF;\r\n\t\t\t\t\ttransition: all 100ms cubic-bezier(.36,.66,.04,1);\r\n\t\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 138, 25,0.1), 0rpx 6rpx 16rpx 0rpx rgba(255, 138, 25,0.1);\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\tbackground-color: #e27f29;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4bd6864f&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4bd6864f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749956144050\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}