{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?a367", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?e447", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?5d3c", "uni-app:///pages/index/practise.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?af83", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?2b22"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "showOverlay", "feedbackValue", "questionList", "id", "type", "schedule", "content", "options", "label", "<PERSON><PERSON><PERSON><PERSON>", "analysis", "userAnswer", "hasAnswered", "showAnalysis", "isCollected", "currentQuestionIndex", "totalQuestions", "computed", "currentQuestion", "has<PERSON>revious", "hasNext", "progressText", "nextProgressText", "answeredCount", "correctCount", "collectedCount", "wrongCount", "methods", "selectAnswer", "currentQ", "getOptionClass", "goToPrevious", "goToNext", "resetCurrentAnswer", "toggleCollect", "uni", "title", "icon", "onFeedback", "onPractise", "viewAnswerRecord", "isCorrect", "console", "url", "closeOverlay", "submitFeedback", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onReachBottom", "onPullDownRefresh", "onBackPress", "onPageScroll", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,2UAEN;AACP,KAAK;AACL;AACA,aAAa,mTAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAA6qB,CAAgB,ksBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiFjsB;EACAC;EACAC;IAAA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC,eACA;QACAC;QACAC;QACAC;QACAC;QACAC,UACA;UAAAC;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,EACA;QACAG;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA,GACA;QACAX;QACAC;QACAC;QACAC;QACAC,UACA;UAAAC;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,EACA;QACAG;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAX;QACAC;QACAC;QACAC;QACAC,UACA;UAAAC;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,EACA;QACAG;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACA;MACAC;MACA;MACAC;IACA;EAAA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACA;IACAC;MACA;QAAA;MAAA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;;MAEA;MACAC;MACAA;MACAA;IACA;IAEA;IACAC;MACA;MACA;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAJ;MACAA;MACAA;IACA;IAEA;IACAK;MACA;MACAL;MAEAM;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;UACA;YACA;UACA;YACAJ;cACAC;cACAC;YACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;YACA;UACA;UACA;QACA;UACA;UACA;UACA;MAAA;IAEA;IAEA;IACAG;MACA;QAAA;UACArC;UACAE;UACAM;UACAF;UACAgC;UACA7B;UACAE;QACA;MAAA;MACA;MACA;MACA4B;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACA;MACAP;QACAQ;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAV;QACAC;QACAC;MACA;MACA;MACA;IACA;EACA;EACA;EACAS,kCAEA;EACA;EACAC;EACA;EACAC,2BAEA;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC3VA;AAAA;AAAA;AAAA;AAA4yC,CAAgB,ywCAAG,EAAC,C;;;;;;;;;;;ACAh0C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/practise.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/practise.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./practise.vue?vue&type=template&id=59d936fe&scoped=true&\"\nvar renderjs\nimport script from \"./practise.vue?vue&type=script&lang=js&\"\nexport * from \"./practise.vue?vue&type=script&lang=js&\"\nimport style0 from \"./practise.vue?vue&type=style&index=0&id=59d936fe&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"59d936fe\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/practise.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=template&id=59d936fe&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-navbar/u-navbar\" */ \"@/uni_modules/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uGap: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-gap/u-gap\" */ \"@/uni_modules/uview-ui/components/u-gap/u-gap.vue\"\n      )\n    },\n    liuDragButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button\" */ \"@/uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue\"\n      )\n    },\n    uSafeBottom: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom\" */ \"@/uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue\"\n      )\n    },\n    uOverlay: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-overlay/u-overlay\" */ \"@/uni_modules/uview-ui/components/u-overlay/u-overlay.vue\"\n      )\n    },\n    uTextarea: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-textarea/u-textarea\" */ \"@/uni_modules/uview-ui/components/u-textarea/u-textarea.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.currentQuestion.options, function (option, __i0__) {\n    var $orig = _vm.__get_orig(option)\n    var m0 = _vm.getOptionClass(option)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"practise-content\">\n\t\t<u-navbar :placeholder=\"true\" bgColor=\"#FFD101\" :autoBack=\"true\">\n\t\t\t<view class=\"u-nav-slot\" slot=\"left\">\n\t\t\t\t<image class=\"arrow\" src=\"@/static/images/arrow-left.png\" />\n\t\t\t\t<text class=\"label\">答题</text>\n\t\t\t\t<text class=\"value\">{{ progressText }}</text>\n\t\t\t</view>\n\t\t</u-navbar>\n\t\t<view class=\"practise-box\">\n\t\t\t<view class=\"practise-type\">\n\t\t\t\t<text class=\"type\">{{ currentQuestion.type }}</text>\n\t\t\t\t<text class=\"schedule\">{{ currentQuestion.schedule }}</text>\n\t\t\t</view>\n\t\t\t<text class=\"content\" :user-select=\"true\">{{ currentQuestion.content }}</text>\n\t\t\t<view class=\"options-list\">\n\t\t\t\t<view class=\"options-li\" :class=\"[getOptionClass(option)]\" v-for=\"option in currentQuestion.options\"\n\t\t\t\t\t:key=\"option.label\" @click=\"selectAnswer(option)\">\n\t\t\t\t\t<text class=\"option-label\">{{ option.label }}</text>\n\t\t\t\t\t<text class=\"option-content\">{{ option.content }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"correct-answer\" v-if=\"currentQuestion.hasAnswered\">\n\t\t\t\t<text class=\"label\">正确答案：</text>\n\t\t\t\t<text class=\"value\">{{ currentQuestion.correctAnswer }}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<u-gap height=\"22rpx\" bgColor=\"#F7F7F7\" />\n\t\t<view class=\"answer-analysis\" v-if=\"currentQuestion.showAnalysis\">\n\t\t\t<view class=\"analysis-title\">\n\t\t\t\t<text class=\"title\">答题解析</text>\n\t\t\t\t<text class=\"icon\"></text>\n\t\t\t</view>\n\t\t\t<text class=\"analysis-content\" :user-select=\"true\">{{ currentQuestion.analysis }}</text>\n\t\t</view>\n\t\t<liu-drag-button v-if=\"!showOverlay\" @clickBtn=\"onFeedback\" :bottomPx=\"10\" :rightPx=\"5\">\n\t\t\t<view class=\"feedback\">\n\t\t\t\t<image class=\"icon\" src=\"@/static/images/feedback.png\" />\n\t\t\t\t<text class=\"text\">反馈</text>\n\t\t\t</view>\n\t\t</liu-drag-button>\n\t\t<u-gap height=\"240rpx\" bgColor=\"transparent\" />\n\t\t<u-safe-bottom />\n\t\t<view class=\"position-bottom\">\n\t\t\t<view class=\"button-list\">\n\t\t\t\t<button class=\"u-reset-button button\" :class=\"{ 'button-s': hasPrevious }\" @click=\"onPractise(1)\">\n\t\t\t\t\t<text class=\"text\">上一题</text>\n\t\t\t\t</button>\n\t\t\t\t<button class=\"u-reset-button button button-s\" @click=\"onPractise(2)\">\n\t\t\t\t\t<image class=\"icon\" v-if=\"!currentQuestion.isCollected\" src=\"@/static/images/collect.png\" />\n\t\t\t\t\t<text class=\"text\">{{ currentQuestion.isCollected ? '已收藏' : '收藏' }}</text>\n\t\t\t\t</button>\n\t\t\t\t<button class=\"u-reset-button button button-s next-step\" v-if=\"hasNext\" @click=\"onPractise(3)\">\n\t\t\t\t\t<text class=\"text\">下一题</text>\n\t\t\t\t\t<text class=\"value\">{{ nextProgressText }}</text>\n\t\t\t\t</button>\n\t\t\t\t<button class=\"u-reset-button button button-s next-step\" v-else @click=\"onPractise(4)\">\n\t\t\t\t\t<text class=\"text\">提交</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t\t<u-safe-bottom />\n\t\t</view>\n\t\t<u-overlay :show=\"showOverlay\" opacity=\"0.87\">\n\t\t\t<view class=\"feedback-overlay\">\n\t\t\t\t<view class=\"feedback-box\">\n\t\t\t\t\t<text class=\"box-title\">请描述您需要反馈的内容</text>\n\t\t\t\t\t<view class=\"textarea-item\">\n\t\t\t\t\t\t<u-textarea height=\"470rpx\" v-model=\"feedbackValue\" placeholder=\"请输入反馈内容\" :maxlength=\"500\"\n\t\t\t\t\t\t\tcount></u-textarea>\n\t\t\t\t\t</view>\n\t\t\t\t\t<button class=\"u-reset-button submit-feedback\" @click=\"submitFeedback\">提交反馈</button>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"close-icon\" @click=\"closeOverlay\">\n\t\t\t\t\t<u-icon name=\"close\" color=\"#000\" bold size=\"50rpx\" />\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-overlay>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tcomponents: {},\n\tdata: () => ({\n\t\tshowOverlay: false, // 反馈弹窗\n\t\tfeedbackValue: '', // 反馈内容\n\t\t// 题目数组\n\t\tquestionList: [\n\t\t\t{\n\t\t\t\tid: 1,\n\t\t\t\ttype: '单选题',\n\t\t\t\tschedule: '1-1',\n\t\t\t\tcontent: '1、写一篇描写课间操场景的作文，运用 \"七种武器\"，以下哪项能使作文内容更丰富呢？（ ）',\n\t\t\t\toptions: [\n\t\t\t\t\t{ label: 'A', content: '只写同学们在做课间操的动作' },\n\t\t\t\t\t{ label: 'B', content: '从看到同学们整齐的队列、听到广播里的音乐声、闻到操场边树木的气息、摸到做操用的器材等多方面去写，同时写自己充满活力的心情' },\n\t\t\t\t\t{ label: 'C', content: '只写自己想象中下次课间操想怎么改进' },\n\t\t\t\t\t{ label: 'D', content: '只写看到的几个同学打闹的样子' }\n\t\t\t\t],\n\t\t\t\tcorrectAnswer: 'B',\n\t\t\t\tanalysis: '写作文时运用\"七种武器\"（视觉、听觉、嗅觉、触觉、味觉、心理感受、想象）能让文章更加生动丰富。选项B正确地体现了多感官描写的特点，通过看、听、闻、摸等多个角度来描写课间操场景，同时加入心理感受，这样的写作方式能使文章内容更加丰富立体。',\n\t\t\t\tuserAnswer: '', // 用户选择的答案\n\t\t\t\thasAnswered: false, // 是否已答题\n\t\t\t\tshowAnalysis: false, // 是否显示解析\n\t\t\t\tisCollected: false // 是否已收藏\n\t\t\t},\n\t\t\t{\n\t\t\t\tid: 2,\n\t\t\t\ttype: '单选题',\n\t\t\t\tschedule: '1-2',\n\t\t\t\tcontent: '2、在描写人物时，以下哪种方法最能突出人物的性格特点？（ ）',\n\t\t\t\toptions: [\n\t\t\t\t\t{ label: 'A', content: '只描写人物的外貌特征' },\n\t\t\t\t\t{ label: 'B', content: '通过人物的语言、动作、心理活动等多方面来刻画' },\n\t\t\t\t\t{ label: 'C', content: '只写人物做了什么事情' },\n\t\t\t\t\t{ label: 'D', content: '只描写人物的穿着打扮' }\n\t\t\t\t],\n\t\t\t\tcorrectAnswer: 'B',\n\t\t\t\tanalysis: '人物描写要立体化，需要通过多个维度来刻画。语言描写能体现人物的文化水平和性格；动作描写能反映人物的习惯和特点；心理活动描写能展现人物的内心世界。综合运用这些方法，才能塑造出鲜活的人物形象。',\n\t\t\t\tuserAnswer: '',\n\t\t\t\thasAnswered: false,\n\t\t\t\tshowAnalysis: false,\n\t\t\t\tisCollected: false\n\t\t\t},\n\t\t\t{\n\t\t\t\tid: 3,\n\t\t\t\ttype: '单选题',\n\t\t\t\tschedule: '1-3',\n\t\t\t\tcontent: '3、写记叙文时，以下哪种开头方式最能吸引读者？（ ）',\n\t\t\t\toptions: [\n\t\t\t\t\t{ label: 'A', content: '直接说明时间、地点、人物' },\n\t\t\t\t\t{ label: 'B', content: '用生动的场景描写或引人深思的问题开头' },\n\t\t\t\t\t{ label: 'C', content: '先介绍文章要写什么内容' },\n\t\t\t\t\t{ label: 'D', content: '用\"今天我要写...\"这样的句式' }\n\t\t\t\t],\n\t\t\t\tcorrectAnswer: 'B',\n\t\t\t\tanalysis: '好的开头应该能够吸引读者的注意力，激发阅读兴趣。生动的场景描写能让读者身临其境，引人深思的问题能激发读者的好奇心。这样的开头比平铺直叙更有吸引力，能让文章从一开始就抓住读者的心。',\n\t\t\t\tuserAnswer: '',\n\t\t\t\thasAnswered: false,\n\t\t\t\tshowAnalysis: false,\n\t\t\t\tisCollected: false\n\t\t\t}\n\t\t],\n\t\t// 当前题目索引\n\t\tcurrentQuestionIndex: 0,\n\t\t// 总题目数量\n\t\ttotalQuestions: 3\n\t}),\n\tcomputed: {\n\t\t// 当前题目数据\n\t\tcurrentQuestion() {\n\t\t\treturn this.questionList[this.currentQuestionIndex];\n\t\t},\n\t\t// 是否有上一题\n\t\thasPrevious() {\n\t\t\treturn this.currentQuestionIndex > 0;\n\t\t},\n\t\t// 是否有下一题\n\t\thasNext() {\n\t\t\treturn this.currentQuestionIndex < this.questionList.length - 1;\n\t\t},\n\t\t// 当前进度显示\n\t\tprogressText() {\n\t\t\treturn `${this.currentQuestionIndex + 1} / ${this.questionList.length}`;\n\t\t},\n\t\t// 下一题进度显示\n\t\tnextProgressText() {\n\t\t\treturn `${this.currentQuestionIndex + 2}/${this.questionList.length}`;\n\t\t},\n\t\t// 已答题数量\n\t\tansweredCount() {\n\t\t\treturn this.questionList.filter(q => q.hasAnswered).length;\n\t\t},\n\t\t// 正确答题数量\n\t\tcorrectCount() {\n\t\t\treturn this.questionList.filter(q => q.hasAnswered && q.userAnswer === q.correctAnswer).length;\n\t\t},\n\t\t// 收藏题目数量\n\t\tcollectedCount() {\n\t\t\treturn this.questionList.filter(q => q.isCollected).length;\n\t\t},\n\t\t// 错题数量\n\t\twrongCount() {\n\t\t\treturn this.questionList.filter(q => q.hasAnswered && q.userAnswer !== q.correctAnswer).length;\n\t\t}\n\t},\n\tmethods: {\n\t\t// 选择答案\n\t\tselectAnswer(option) {\n\t\t\tconst currentQ = this.currentQuestion;\n\t\t\tif (currentQ.hasAnswered) return; // 已经答题则不能再选择\n\n\t\t\t// 记录用户答案到当前题目\n\t\t\tcurrentQ.userAnswer = option.label;\n\t\t\tcurrentQ.hasAnswered = true;\n\t\t\tcurrentQ.showAnalysis = true;\n\t\t},\n\n\t\t// 判断选项样式类\n\t\tgetOptionClass(option) {\n\t\t\tconst currentQ = this.currentQuestion;\n\t\t\tif (!currentQ.hasAnswered) return '';\n\n\t\t\tif (option.label === currentQ.correctAnswer) {\n\t\t\t\t// 正确答案显示绿色\n\t\t\t\treturn 'option-correct';\n\t\t\t} else if (option.label === currentQ.userAnswer) {\n\t\t\t\t// 用户选择的错误答案显示红色\n\t\t\t\treturn 'option-wrong';\n\t\t\t}\n\t\t\treturn '';\n\t\t},\n\n\t\t// 上一题\n\t\tgoToPrevious() {\n\t\t\tif (this.hasPrevious) {\n\t\t\t\tthis.currentQuestionIndex--;\n\t\t\t}\n\t\t},\n\n\t\t// 下一题\n\t\tgoToNext() {\n\t\t\tif (this.hasNext) {\n\t\t\t\tthis.currentQuestionIndex++;\n\t\t\t}\n\t\t},\n\n\t\t// 重置当前题目答题状态\n\t\tresetCurrentAnswer() {\n\t\t\tconst currentQ = this.currentQuestion;\n\t\t\tcurrentQ.userAnswer = '';\n\t\t\tcurrentQ.hasAnswered = false;\n\t\t\tcurrentQ.showAnalysis = false;\n\t\t},\n\n\t\t// 切换收藏状态\n\t\ttoggleCollect() {\n\t\t\tconst currentQ = this.currentQuestion;\n\t\t\tcurrentQ.isCollected = !currentQ.isCollected;\n\n\t\t\tuni.showToast({\n\t\t\t\ttitle: currentQ.isCollected ? '收藏成功' : '取消收藏',\n\t\t\t\ticon: currentQ.isCollected ? 'success' : 'none'\n\t\t\t});\n\t\t},\n\n\t\t// 反馈功能\n\t\tonFeedback() {\n\t\t\tthis.showOverlay = true;\n\t\t},\n\n\t\t// 练习功能（上一题/下一题/收藏）\n\t\tonPractise(type) {\n\t\t\tswitch (type) {\n\t\t\t\tcase 1:\n\t\t\t\t\t// 上一题\n\t\t\t\t\tif (this.hasPrevious) {\n\t\t\t\t\t\tthis.goToPrevious();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '已经是第一题了',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\t// 收藏功能\n\t\t\t\t\tthis.toggleCollect();\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\t// 下一题\n\t\t\t\t\tif (this.hasNext) {\n\t\t\t\t\t\tthis.goToNext();\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase 4:\n\t\t\t\t\t// 提交\n\t\t\t\t\tthis.viewAnswerRecord();\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\n\t\t// 查看答题记录\n\t\tviewAnswerRecord() {\n\t\t\tconst record = this.questionList.map(q => ({\n\t\t\t\tid: q.id,\n\t\t\t\tschedule: q.schedule,\n\t\t\t\tuserAnswer: q.userAnswer,\n\t\t\t\tcorrectAnswer: q.correctAnswer,\n\t\t\t\tisCorrect: q.userAnswer === q.correctAnswer,\n\t\t\t\thasAnswered: q.hasAnswered,\n\t\t\t\tisCollected: q.isCollected\n\t\t\t}));\n\t\t\t// 计算正确率（0-100的整数）\n\t\t\tconst scoreValue = this.answeredCount > 0 ? Math.round((this.correctCount / this.answeredCount) * 100) : 0;\n\t\t\tconsole.log('答题记录：', record);\n\t\t\tconsole.log(`总题数：${this.questionList.length}`);\n\t\t\tconsole.log(`已答题：${this.answeredCount}`);\n\t\t\tconsole.log(`正确数：${this.correctCount}`);\n\t\t\tconsole.log(`错题数：${this.wrongCount}`);\n\t\t\tconsole.log(`收藏数：${this.collectedCount}`);\n\t\t\tconsole.log(`正确率：${scoreValue}%`);\n\t\t\t// 跳转到答题统计页面\n\t\t\tuni.redirectTo({\n\t\t\t\turl: `/pages/index/success?a=${this.answeredCount}&c=${this.correctCount}&w=${this.wrongCount}&s=${scoreValue}`\n\t\t\t})\n\t\t},\n\t\t// 关闭弹窗\n\t\tcloseOverlay() {\n\t\t\tthis.showOverlay = false; // 关闭弹窗\n\t\t},\n\t\t// 提交反馈\n\t\tsubmitFeedback() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '反馈已提交',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t\tthis.showOverlay = false; // 关闭弹窗\n\t\t\tthis.feedbackValue = ''; // 清空反馈内容\n\t\t},\n\t},\n\t// 页面周期函数--监听页面加载\n\tonLoad(options) {\n\n\t},\n\t// 页面周期函数--监听页面初次渲染完成\n\tonReady() { },\n\t// 页面周期函数--监听页面显示(not-nvue)\n\tonShow() {\n\n\t},\n\t// 页面周期函数--监听页面隐藏\n\tonHide() { },\n\t// 页面周期函数--监听页面卸载\n\tonUnload() { },\n\t// 页面处理函数--监听用户上拉触底\n\tonReachBottom() { },\n\t// 页面处理函数--监听用户下拉动作\n\tonPullDownRefresh() { },\n\t// 页面周期函数--监听页面返回\n\tonBackPress() { },\n\t// 页面处理函数--监听页面滚动(not-nvue)\n\tonPageScroll(e) { },\n\t// 页面处理函数--用户点击右上角分享好友\n\tonShareAppMessage(options) { },\n\t// 页面处理函数--用户点击右上角分享朋友圈\n\tonShareTimeline(options) { }\n};\n</script>\n<style lang=\"scss\" scoped>\n.practise-content {\n\twidth: 100%;\n\tmin-height: 100vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground-image: linear-gradient(to bottom, #FFD101 10%, #FFF 50%);\n\n\t.u-nav-slot {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t.arrow {\n\t\t\twidth: 30rpx;\n\t\t\theight: 30rpx;\n\t\t}\n\n\t\t.label {\n\t\t\tfont-size: 36rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #000000;\n\t\t\tmargin: 0 15rpx 0 5rpx;\n\t\t}\n\n\t\t.value {\n\t\t\tfont-size: 30rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #000000;\n\t\t}\n\t}\n\n\t.practise-box {\n\t\twidth: 100%;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 43rpx 43rpx 0 0;\n\t\tmargin-top: 15rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 40rpx 25rpx;\n\n\t\t.practise-type {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 30rpx;\n\n\t\t\t.type {\n\t\t\t\theight: 58rpx;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #000000;\n\t\t\t\tline-height: 58rpx;\n\t\t\t\tpadding: 0 17rpx;\n\t\t\t\tbackground-color: #FFF8D8;\n\t\t\t\tborder-radius: 7rpx;\n\t\t\t\tborder: 2rpx solid #FFE674;\n\t\t\t\tmargin-right: 25rpx;\n\t\t\t}\n\n\t\t\t.schedule {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #464646;\n\t\t\t}\n\t\t}\n\n\t\t.content {\n\t\t\twidth: 100%;\n\t\t\tfont-size: 36rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #000000;\n\t\t\tline-height: 52rpx;\n\t\t\tmargin-bottom: 30rpx;\n\t\t}\n\n\t\t.options-list {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\n\t\t\t.options-li {\n\t\t\t\twidth: 100%;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tpadding: 25rpx 0;\n\t\t\t\tborder-radius: 20rpx;\n\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\n\t\t\t\t&:active {\n\t\t\t\t\t.option-content {\n\t\t\t\t\t\ttext-decoration: underline wavy #c0c4cc;\n\t\t\t\t\t}\n\n\t\t\t\t\tbackground-color: #f9f9f9;\n\t\t\t\t}\n\n\t\t\t\t&.option-correct {\n\t\t\t\t\t.option-label {\n\t\t\t\t\t\tbackground-color: #65BC17;\n\t\t\t\t\t\tborder-color: #65BC17;\n\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t}\n\n\t\t\t\t\t.option-content {\n\t\t\t\t\t\tcolor: #55B300;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.option-wrong {\n\n\t\t\t\t\t.option-label {\n\t\t\t\t\t\tbackground-color: #F4621A;\n\t\t\t\t\t\tborder-color: #F4621A;\n\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t}\n\n\t\t\t\t\t// .option-content {\n\t\t\t\t\t// \tcolor: #F44336;\n\t\t\t\t\t// }\n\t\t\t\t}\n\n\t\t\t\t.option-label {\n\t\t\t\t\twidth: 50rpx;\n\t\t\t\t\theight: 50rpx;\n\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\tline-height: 50rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\tcolor: #000000;\n\t\t\t\t\tborder-radius: 46rpx;\n\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\tbackground-color: #FFF;\n\t\t\t\t\tborder: 2rpx solid #B2B2B2;\n\t\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\t\t\t\t}\n\n\t\t\t\t.option-content {\n\t\t\t\t\twidth: 630rpx;\n\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\tcolor: #464646;\n\t\t\t\t\tline-height: 50rpx;\n\t\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.correct-answer {\n\t\t\twidth: 100%;\n\t\t\theight: 86rpx;\n\t\t\tbackground-color: #E9F8D1;\n\t\t\tborder-radius: 10rpx;\n\t\t\tpadding: 0 30rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmargin: 40rpx 0;\n\n\t\t\t.label {\n\t\t\t\tfont-size: 36rpx;\n\t\t\t\tline-height: 86rpx;\n\t\t\t\tcolor: #459101;\n\t\t\t}\n\n\t\t\t.value {\n\t\t\t\tfont-size: 36rpx;\n\t\t\t\tline-height: 40rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #459101;\n\t\t\t}\n\t\t}\n\t}\n\n\t.answer-analysis {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 20rpx 25rpx;\n\n\t\t.analysis-title {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 35rpx;\n\n\t\t\t.title {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #000000;\n\t\t\t\tz-index: 10;\n\t\t\t}\n\n\t\t\t.icon {\n\t\t\t\twidth: 126rpx;\n\t\t\t\theight: 15rpx;\n\t\t\t\tbackground-image: linear-gradient(to right, #FFD101 0%, #FFFFFF 100%);\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tposition: relative;\n\t\t\t\tbottom: 10rpx;\n\t\t\t}\n\t\t}\n\n\t\t.analysis-content {\n\t\t\tfont-size: 36rpx;\n\t\t\tcolor: #000000;\n\t\t\tline-height: 52rpx;\n\t\t}\n\t}\n\n\t.feedback {\n\t\tposition: fixed;\n\t\tleft: 25rpx;\n\t\tbottom: 240rpx;\n\t\twidth: 68rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-direction: column;\n\t\ttransition: all 100ms cubic-bezier(.36, .66, .04, 1);\n\n\t\t&:active {\n\t\t\ttransform: scale(0.99);\n\t\t}\n\n\t\t.icon {\n\t\t\twidth: 68rpx;\n\t\t\theight: 62rpx;\n\t\t}\n\n\t\t.text {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #464646;\n\t\t}\n\t}\n\n\t.position-bottom {\n\t\tposition: fixed;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 25rpx 20rpx;\n\t\tbackground-color: #FFF;\n\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);\n\t\tborder-top: 1rpx solid #e4e7ed;\n\n\t\t.button-list {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\n\t\t\t.button {\n\t\t\t\twidth: 186rpx;\n\t\t\t\theight: 80rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tbackground-color: #DDDDDD;\n\t\t\t\tborder-radius: 21rpx;\n\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\t\t\t\tmargin: 0;\n\n\t\t\t\t.icon {\n\t\t\t\t\twidth: 50rpx;\n\t\t\t\t\theight: 50rpx;\n\t\t\t\t\tmargin-right: 10rpx;\n\t\t\t\t}\n\n\t\t\t\t.text {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #B2B2B2;\n\t\t\t\t}\n\n\t\t\t\t.value {\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #464646;\n\t\t\t\t}\n\n\t\t\t\t&.button-s {\n\t\t\t\t\tbackground-color: #FFD101;\n\t\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);\n\n\t\t\t\t\t.text {\n\t\t\t\t\t\tcolor: #000;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\tbackground-color: #f7ca00;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.button-collected {\n\t\t\t\t\tbackground-color: #FF6B35;\n\t\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 107, 53, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 107, 53, 0.2);\n\n\t\t\t\t\t.text {\n\t\t\t\t\t\tcolor: #FFF;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\tbackground-color: #e55a2b;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.next-step {\n\t\t\t\t\tmin-width: 272rpx;\n\t\t\t\t\tpadding: 0 3rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.feedback-overlay {\n\t\twidth: 100%;\n\t\theight: 100vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\n\t\t.feedback-box {\n\t\t\twidth: 650rpx;\n\t\t\tbackground-color: #FFF;\n\t\t\tborder-radius: 15rpx;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tpadding: 55rpx 50rpx 72rpx;\n\n\t\t\t.box-title {\n\t\t\t\tfont-size: 36rpx;\n\t\t\t\tcolor: #000000;\n\t\t\t\tmargin-bottom: 34rpx;\n\t\t\t}\n\n\t\t\t.textarea-item {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\n\t\t\t.submit-feedback {\n\t\t\t\twidth: 370rpx;\n\t\t\t\theight: 104rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tbackground-color: #FFD101;\n\t\t\t\tborder-radius: 21rpx;\n\t\t\t\tfont-size: 36rpx;\n\t\t\t\tcolor: #000000;\n\t\t\t\tmargin-top: 60rpx;\n\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);\n\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\n\t\t\t\t&:active {\n\t\t\t\t\tbackground-color: #f7ca00;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.close-icon {\n\t\t\twidth: 85rpx;\n\t\t\theight: 85rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tmargin-top: 60rpx;\n\t\t\tborder-radius: 50%;\n\t\t\tborder: 4px solid #000;\n\t\t\tbackground-color: #B2B2B2;\n\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\n\t\t\t&:active {\n\t\t\t\tbackground-color: #9f9f9f;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=style&index=0&id=59d936fe&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=style&index=0&id=59d936fe&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749966730759\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}