{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?a367", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?e447", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?5d3c", "uni-app:///pages/index/practise.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?af83", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?2b22"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "filters", "methods", "onFunction", "uni", "url", "openPrivacyContract", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onReachBottom", "onPullDownRefresh", "onBackPress", "onPageScroll", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,2UAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAA6qB,CAAgB,ksBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0EjsB;EACAC;EACAC;IAAA,QAEA;EAAA;EACAC;EACAC;IACA;IACAC;MACA;QACA;UACA;UACAC;YACAC;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;UACA;MAAA;IAEA;IACA;IACAC;MACAF;IACA;EAEA;EACA;EACAG,kCAEA;EACA;EACAC;EACA;EACAC,2BAEA;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC1JA;AAAA;AAAA;AAAA;AAA4yC,CAAgB,ywCAAG,EAAC,C;;;;;;;;;;;ACAh0C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/practise.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/practise.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./practise.vue?vue&type=template&id=59d936fe&scoped=true&\"\nvar renderjs\nimport script from \"./practise.vue?vue&type=script&lang=js&\"\nexport * from \"./practise.vue?vue&type=script&lang=js&\"\nimport style0 from \"./practise.vue?vue&type=style&index=0&id=59d936fe&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"59d936fe\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/practise.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=template&id=59d936fe&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-navbar/u-navbar\" */ \"@/uni_modules/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uGap: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-gap/u-gap\" */ \"@/uni_modules/uview-ui/components/u-gap/u-gap.vue\"\n      )\n    },\n    liuDragButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button\" */ \"@/uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue\"\n      )\n    },\n    uSafeBottom: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom\" */ \"@/uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"practise-content\">\r\n\t\t<u-navbar :placeholder=\"true\" bgColor=\"#FFD101\" :autoBack=\"true\">\r\n\t\t\t<view class=\"u-nav-slot\" slot=\"left\">\r\n\t\t\t\t<image class=\"arrow\" src=\"@/static/images/arrow-left.png\" />\r\n\t\t\t\t<text class=\"label\">答题</text>\r\n\t\t\t\t<text class=\"value\">30 / 456</text>\r\n\t\t\t</view>\r\n\t\t</u-navbar>\r\n\t\t<view class=\"practise-box\">\r\n\t\t\t<view class=\"practise-type\">\r\n\t\t\t\t<text class=\"type\">单选题</text>\r\n\t\t\t\t<text class=\"schedule\">1-1</text>\r\n\t\t\t</view>\r\n\t\t\t<text class=\"content\" :user-select=\"true\">1、写一篇描写课间操场景的作文，运用 “七种武器”，以下哪项能使作文内容更丰富呢？（ ）</text>\r\n\t\t\t<view class=\"options-list\">\r\n\t\t\t\t<view class=\"options-li\">\r\n\t\t\t\t\t<text class=\"option-label\">A</text>\r\n\t\t\t\t\t<text class=\"option-content\">只写同学们在做课间操的动作</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"options-li\">\r\n\t\t\t\t\t<text class=\"option-label\">B</text>\r\n\t\t\t\t\t<text class=\"option-content\">从看到同学们整齐的队列、听到广播里的音乐声、闻到操场边树木的气息、摸到做操用的器材等多方面去写，同时写自己充满活力的心情</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"options-li\">\r\n\t\t\t\t\t<text class=\"option-label\">C</text>\r\n\t\t\t\t\t<text class=\"option-content\">只写自己想象中下次课间操想怎么改进</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"options-li\">\r\n\t\t\t\t\t<text class=\"option-label\">D</text>\r\n\t\t\t\t\t<text class=\"option-content\">只写看到的几个同学打闹的样子</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"correct-answer\">\r\n\t\t\t\t<text class=\"label\">正确答案：</text>\r\n\t\t\t\t<text class=\"value\">A</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<u-gap height=\"22rpx\" bgColor=\"#F7F7F7\" />\r\n\t\t<view class=\"answer-analysis\">\r\n\t\t\t<view class=\"analysis-title\">\r\n\t\t\t\t<text class=\"title\">答题解析</text>\r\n\t\t\t\t<text class=\"icon\"></text>\r\n\t\t\t</view>\r\n\t\t\t<text class=\"analysis-content\" :user-select=\"true\">原文：“他这么想着，扇子摇得更勤了。扇子常常碰在身体上，发出啪啪的声音。他不会叫喊，这是唯一的警告主人的法子了。</text>\r\n\t\t</view>\r\n\t\t<liu-drag-button @clickBtn=\"onFeedback\" :bottomPx=\"10\" :rightPx=\"5\">\r\n\t\t\t<view class=\"feedback\">\r\n\t\t\t\t<image class=\"icon\" src=\"@/static/images/feedback.png\" />\r\n\t\t\t\t<text class=\"text\">反馈</text>\r\n\t\t\t</view>\r\n\t\t</liu-drag-button>\r\n\t\t<u-gap height=\"240rpx\" bgColor=\"transparent\" />\r\n\t\t<u-safe-bottom />\r\n\t\t<view class=\"position-bottom\">\r\n\t\t\t<view class=\"button-list\">\r\n\t\t\t\t<button class=\"u-reset-button button\" :class=\"{ 'button-s': isNextStep }\" @click=\"onPractise(1)\">\r\n\t\t\t\t\t<text class=\"text\">上一题</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t<button class=\"u-reset-button button button-s\" @click=\"onPractise(2)\">\r\n\t\t\t\t\t<image class=\"icon\" src=\"@/static/images/collect.png\" />\r\n\t\t\t\t\t<text class=\"text\">收藏</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t<button class=\"u-reset-button button button-s next-step\" @click=\"onPractise(2)\">\r\n\t\t\t\t\t<text class=\"text\">下一题</text>\r\n\t\t\t\t\t<text class=\"value\">2/456</text>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t\t<u-safe-bottom />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tcomponents: {},\r\n\t\tdata: () => ({\r\n\t\t\t\r\n\t\t}),\r\n\t\tfilters: {},\r\n\t\tmethods: {\r\n\t\t\t// 功能跳转\r\n\t\t\tonFunction(e) {\r\n\t\t\t\tswitch (e) {\n\t\t\t\t\tcase 1:\n\t\t\t\t\t\t// 错题本\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/wrongBook'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 2:\n\t\t\t\t\t\t// 我的收藏\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/myCollection'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 3:\n\t\t\t\t\t\t// 顺序练习\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/sequencePractice'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 4:\n\t\t\t\t\t\t// 随机练习\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/randomPractice'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 5:\n\t\t\t\t\t\t// 自选练习\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/customPractice'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 6:\n\t\t\t\t\t\t// 购买纸质版\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/purchasePaperVersion'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 跳转至隐私协议页面\r\n\t\t\topenPrivacyContract() {\r\n\t\t\t\tuni.openPrivacyContract()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面加载\r\n\t\tonLoad(options) {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面初次渲染完成\r\n\t\tonReady() {},\r\n\t\t// 页面周期函数--监听页面显示(not-nvue)\r\n\t\tonShow() {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面隐藏\r\n\t\tonHide() {},\r\n\t\t// 页面周期函数--监听页面卸载\r\n\t\tonUnload() {},\r\n\t\t// 页面处理函数--监听用户上拉触底\r\n\t\tonReachBottom() {},\r\n\t\t// 页面处理函数--监听用户下拉动作\r\n\t\tonPullDownRefresh() {},\r\n\t\t// 页面周期函数--监听页面返回\r\n\t\tonBackPress() {},\r\n\t\t// 页面处理函数--监听页面滚动(not-nvue)\r\n\t\tonPageScroll(e) {},\r\n\t\t// 页面处理函数--用户点击右上角分享好友\r\n\t\tonShareAppMessage(options) {},\r\n\t\t// 页面处理函数--用户点击右上角分享朋友圈\r\n\t\tonShareTimeline(options) {}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.practise-content {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 100vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbackground-image: linear-gradient(to bottom, #FFD101 10%, #FFF 50%);\r\n\t\t.u-nav-slot {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t.arrow {\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t}\r\n\t\t\t.label {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tmargin: 0 15rpx 0 5rpx;\r\n\t\t\t}\r\n\t\t\t.value {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.practise-box {\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\tborder-radius: 43rpx 43rpx 0 0;\r\n\t\t\tmargin-top: 15rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tpadding: 40rpx 25rpx;\r\n\t\t\t.practise-type {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t.type {\r\n\t\t\t\t\theight: 58rpx;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #000000;\r\n\t\t\t\t\tline-height: 58rpx;\r\n\t\t\t\t\tpadding: 0 17rpx;\n\t\t\t\t\tbackground-color: #FFF8D8;\r\n\t\t\t\t\tborder-radius: 7rpx;\r\n\t\t\t\t\tborder: 2rpx solid #FFE674;\r\n\t\t\t\t\tmargin-right: 25rpx;\n\t\t\t\t}\r\n\t\t\t\t.schedule {\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #464646;\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.content {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tline-height: 52rpx;\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t}\r\n\t\t\t.options-list {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\t.options-li {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\tpadding: 25rpx 0;\r\n\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\ttransition: all 100ms cubic-bezier(.36, .66, .04, 1);\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t.option-content {\r\n\t\t\t\t\t\t\ttext-decoration: underline wavy blue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tbackground-color: #f9f9f9;\r\n\t\t\t\t\t}\n\t\t\t\t\t.option-label {\n\t\t\t\t\t\twidth: 50rpx;\n\t\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t\tline-height: 50rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\t\tcolor: #000000;\n\t\t\t\t\t\tborder-radius: 46rpx;\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\tbackground-color: #FFF;\r\n\t\t\t\t\t\tborder: 2rpx solid #B2B2B2;\n\t\t\t\t\t}\n\t\t\t\t\t.option-content {\r\n\t\t\t\t\t\twidth: 630rpx;\n\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t\tcolor: #464646;\r\n\t\t\t\t\t\tline-height: 50rpx;\r\n\t\t\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.correct-answer {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 86rpx;\r\n\t\t\t\tbackground-color: #E9F8D1;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin: 40rpx 0;\r\n\t\t\t\t.label {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tline-height: 86rpx;\r\n\t\t\t\t\tcolor: #459101;\r\n\t\t\t\t}\r\n\t\t\t\t.value {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tline-height: 40rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tcolor: #459101;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.answer-analysis {\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tpadding: 20rpx 25rpx;\r\n\t\t\t.analysis-title {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 35rpx;\r\n\t\t\t\t.title {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #000000;\r\n\t\t\t\t\tz-index: 10;\r\n\t\t\t\t}\r\n\t\t\t\t.icon {\r\n\t\t\t\t\twidth: 126rpx;\r\n\t\t\t\t\theight: 15rpx;\r\n\t\t\t\t\tbackground-image: linear-gradient(to right, #FFD101 0%, #FFFFFF 100%);\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tbottom: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.analysis-content {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tline-height: 52rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\t\t.feedback {\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 25rpx;\r\n\t\t\tbottom: 240rpx;\r\n\t\t\twidth: 68rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: column;\r\n\t\t\ttransition: all 100ms cubic-bezier(.36, .66, .04, 1);\r\n\t\t\t&:active {\n\t\t\t\ttransform: scale(0.99);\n\t\t\t}\r\n\t\t\t.icon {\r\n\t\t\t\twidth: 68rpx;\r\n\t\t\t\theight: 62rpx;\r\n\t\t\t}\r\n\t\t\t.text {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #464646;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.position-bottom {\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\tbottom: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tpadding: 25rpx 20rpx;\r\n\t\t\tbackground-color: #FFF;\r\n\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);\r\n\t\t\tborder-top: 1rpx solid #e4e7ed;\r\n\t\t\t.button-list {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\r\n\t\t\t\t.button {\r\n\t\t\t\t\twidth: 186rpx;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tbackground-color: #DDDDDD;\r\n\t\t\t\t\tborder-radius: 21rpx;\r\n\t\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\t.icon {\r\n\t\t\t\t\t\twidth: 50rpx;\r\n\t\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t\t\t\t.text {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tcolor: #B2B2B2;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.value {\r\n\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #464646;\n\t\t\t\t\t}\r\n\t\t\t\t\t&.button-s {\r\n\t\t\t\t\t\tbackground-color: #FFD101;\r\n\t\t\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);\r\n\t\t\t\t\t\t.text {\r\n\t\t\t\t\t\t\tcolor: #000;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t\tbackground-color: #f7ca00;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t&.next-step {\n\t\t\t\t\t\tmin-width: 272rpx;\r\n\t\t\t\t\t\tpadding: 0 3rpx;\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=style&index=0&id=59d936fe&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=style&index=0&id=59d936fe&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749918813423\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}