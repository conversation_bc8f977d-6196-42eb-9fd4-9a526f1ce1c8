{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?a367", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?e447", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?5d3c", "uni-app:///pages/index/practise.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?af83", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/practise.vue?2b22"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "questionData", "id", "type", "schedule", "content", "options", "label", "<PERSON><PERSON><PERSON><PERSON>", "analysis", "<PERSON><PERSON><PERSON><PERSON>", "hasAnswered", "showAnalysis", "isNextStep", "methods", "selectAnswer", "getOptionClass", "resetAnswer", "onFeedback", "uni", "title", "icon", "onPractise", "onFunction", "url", "openPrivacyContract", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onReachBottom", "onPullDownRefresh", "onBackPress", "onPageScroll", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,2UAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAA6qB,CAAgB,ksBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+DjsB;EACAC;EACAC;IAAA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC,UACA;UAAAC;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,GACA;UAAAE;UAAAF;QAAA,EACA;QACAG;QAAA;QACAC;MACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EAAA;EACAC;IACA;IACAC;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;UACAH;YACAC;YACAC;UACA;UACA;QACA;UACA;UACAF;YACAC;YACAC;UACA;UACA;MAAA;IAEA;IAEA;IACAE;MACA;QACA;UACA;UACAJ;YACAK;UACA;UACA;QACA;UACA;UACAL;YACAK;UACA;UACA;QACA;UACA;UACAL;YACAK;UACA;UACA;QACA;UACA;UACAL;YACAK;UACA;UACA;QACA;UACA;UACAL;YACAK;UACA;UACA;QACA;UACA;UACAL;YACAK;UACA;UACA;MAAA;IAEA;IACA;IACAC;MACAN;IACA;EACA;EACA;EACAO,kCAEA;EACA;EACAC;EACA;EACAC,2BAEA;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC7NA;AAAA;AAAA;AAAA;AAA4yC,CAAgB,ywCAAG,EAAC,C;;;;;;;;;;;ACAh0C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/practise.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/practise.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./practise.vue?vue&type=template&id=59d936fe&scoped=true&\"\nvar renderjs\nimport script from \"./practise.vue?vue&type=script&lang=js&\"\nexport * from \"./practise.vue?vue&type=script&lang=js&\"\nimport style0 from \"./practise.vue?vue&type=style&index=0&id=59d936fe&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"59d936fe\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/practise.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=template&id=59d936fe&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-navbar/u-navbar\" */ \"@/uni_modules/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uGap: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-gap/u-gap\" */ \"@/uni_modules/uview-ui/components/u-gap/u-gap.vue\"\n      )\n    },\n    liuDragButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button\" */ \"@/uni_modules/liu-drag-button/components/liu-drag-button/liu-drag-button.vue\"\n      )\n    },\n    uSafeBottom: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom\" */ \"@/uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.questionData.options, function (option, __i0__) {\n    var $orig = _vm.__get_orig(option)\n    var m0 = _vm.getOptionClass(option)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"practise-content\">\n\t\t<u-navbar :placeholder=\"true\" bgColor=\"#FFD101\" :autoBack=\"true\">\n\t\t\t<view class=\"u-nav-slot\" slot=\"left\">\n\t\t\t\t<image class=\"arrow\" src=\"@/static/images/arrow-left.png\" />\n\t\t\t\t<text class=\"label\">答题</text>\n\t\t\t\t<text class=\"value\">30 / 456</text>\n\t\t\t</view>\n\t\t</u-navbar>\n\t\t<view class=\"practise-box\">\n\t\t\t<view class=\"practise-type\">\n\t\t\t\t<text class=\"type\">{{ questionData.type }}</text>\n\t\t\t\t<text class=\"schedule\">{{ questionData.schedule }}</text>\n\t\t\t</view>\n\t\t\t<text class=\"content\" :user-select=\"true\">{{ questionData.content }}</text>\n\t\t\t<view class=\"options-list\">\n\t\t\t\t<view class=\"options-li\" :class=\"[getOptionClass(option)]\" v-for=\"option in questionData.options\"\n\t\t\t\t\t:key=\"option.label\" @click=\"selectAnswer(option)\">\n\t\t\t\t\t<text class=\"option-label\">{{ option.label }}</text>\n\t\t\t\t\t<text class=\"option-content\">{{ option.content }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"correct-answer\" v-if=\"hasAnswered\">\n\t\t\t\t<text class=\"label\">正确答案：</text>\n\t\t\t\t<text class=\"value\">{{ questionData.correctAnswer }}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<u-gap height=\"22rpx\" bgColor=\"#F7F7F7\" />\n\t\t<view class=\"answer-analysis\" v-if=\"showAnalysis\">\n\t\t\t<view class=\"analysis-title\">\n\t\t\t\t<text class=\"title\">答题解析</text>\n\t\t\t\t<text class=\"icon\"></text>\n\t\t\t</view>\n\t\t\t<text class=\"analysis-content\" :user-select=\"true\">{{ questionData.analysis }}</text>\n\t\t</view>\n\t\t<liu-drag-button @clickBtn=\"onFeedback\" :bottomPx=\"10\" :rightPx=\"5\">\n\t\t\t<view class=\"feedback\">\n\t\t\t\t<image class=\"icon\" src=\"@/static/images/feedback.png\" />\n\t\t\t\t<text class=\"text\">反馈</text>\n\t\t\t</view>\n\t\t</liu-drag-button>\n\t\t<u-gap height=\"240rpx\" bgColor=\"transparent\" />\n\t\t<u-safe-bottom />\n\t\t<view class=\"position-bottom\">\n\t\t\t<view class=\"button-list\">\n\t\t\t\t<button class=\"u-reset-button button\" :class=\"{ 'button-s': isNextStep }\" @click=\"onPractise(1)\">\n\t\t\t\t\t<text class=\"text\">上一题</text>\n\t\t\t\t</button>\n\t\t\t\t<button class=\"u-reset-button button button-s\" @click=\"onPractise(2)\">\n\t\t\t\t\t<image class=\"icon\" src=\"@/static/images/collect.png\" />\n\t\t\t\t\t<text class=\"text\">收藏</text>\n\t\t\t\t</button>\n\t\t\t\t<button class=\"u-reset-button button button-s next-step\" @click=\"onPractise(2)\">\n\t\t\t\t\t<text class=\"text\">下一题</text>\n\t\t\t\t\t<text class=\"value\">2/456</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t\t<u-safe-bottom />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tcomponents: {},\n\tdata: () => ({\n\t\t// 题目数据\n\t\tquestionData: {\n\t\t\tid: 1,\n\t\t\ttype: '单选题',\n\t\t\tschedule: '1-1',\n\t\t\tcontent: '1、写一篇描写课间操场景的作文，运用 \"七种武器\"，以下哪项能使作文内容更丰富呢？（ ）',\n\t\t\toptions: [\n\t\t\t\t{ label: 'A', content: '只写同学们在做课间操的动作' },\n\t\t\t\t{ label: 'B', content: '从看到同学们整齐的队列、听到广播里的音乐声、闻到操场边树木的气息、摸到做操用的器材等多方面去写，同时写自己充满活力的心情' },\n\t\t\t\t{ label: 'C', content: '只写自己想象中下次课间操想怎么改进' },\n\t\t\t\t{ label: 'D', content: '只写看到的几个同学打闹的样子' }\n\t\t\t],\n\t\t\tcorrectAnswer: 'B', // 正确答案\n\t\t\tanalysis: '写作文时运用\"七种武器\"（视觉、听觉、嗅觉、触觉、味觉、心理感受、想象）能让文章更加生动丰富。选项B正确地体现了多感官描写的特点，通过看、听、闻、摸等多个角度来描写课间操场景，同时加入心理感受，这样的写作方式能使文章内容更加丰富立体。'\n\t\t},\n\t\t// 用户选择的答案\n\t\tselectedAnswer: '',\n\t\t// 是否已经答题\n\t\thasAnswered: false,\n\t\t// 是否显示解析\n\t\tshowAnalysis: false,\n\t\t// 下一步按钮状态\n\t\tisNextStep: true\n\t}),\n\tmethods: {\n\t\t// 选择答案\n\t\tselectAnswer(option) {\n\t\t\tif (this.hasAnswered) return; // 已经答题则不能再选择\n\n\t\t\tthis.selectedAnswer = option.label;\n\t\t\tthis.hasAnswered = true;\n\t\t\tthis.showAnalysis = true;\n\t\t},\n\n\t\t// 判断选项样式类\n\t\tgetOptionClass(option) {\n\t\t\tif (!this.hasAnswered) return '';\n\n\t\t\tif (option.label === this.questionData.correctAnswer) {\n\t\t\t\t// 正确答案显示绿色\n\t\t\t\treturn 'option-correct';\n\t\t\t} else if (option.label === this.selectedAnswer) {\n\t\t\t\t// 用户选择的错误答案显示红色\n\t\t\t\treturn 'option-wrong';\n\t\t\t}\n\t\t\treturn '';\n\t\t},\n\n\t\t// 重置答题状态（用于下一题）\n\t\tresetAnswer() {\n\t\t\tthis.selectedAnswer = '';\n\t\t\tthis.hasAnswered = false;\n\t\t\tthis.showAnalysis = false;\n\t\t},\n\n\t\t// 反馈功能\n\t\tonFeedback() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '反馈功能',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\n\t\t// 练习功能（上一题/下一题/收藏）\n\t\tonPractise(type) {\n\t\t\tswitch (type) {\n\t\t\t\tcase 1:\n\t\t\t\t\t// 上一题\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '上一题',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\t// 收藏/下一题\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '功能开发中',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\n\t\t// 功能跳转\n\t\tonFunction(e) {\n\t\t\tswitch (e) {\n\t\t\t\tcase 1:\n\t\t\t\t\t// 错题本\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/index/wrongBook'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\t// 我的收藏\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/index/myCollection'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\t// 顺序练习\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/index/sequencePractice'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 4:\n\t\t\t\t\t// 随机练习\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/index/randomPractice'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 5:\n\t\t\t\t\t// 自选练习\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/index/customPractice'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t\tcase 6:\n\t\t\t\t\t// 购买纸质版\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/index/purchasePaperVersion'\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\t// 跳转至隐私协议页面\n\t\topenPrivacyContract() {\n\t\t\tuni.openPrivacyContract()\n\t\t}\n\t},\n\t// 页面周期函数--监听页面加载\n\tonLoad(options) {\n\n\t},\n\t// 页面周期函数--监听页面初次渲染完成\n\tonReady() { },\n\t// 页面周期函数--监听页面显示(not-nvue)\n\tonShow() {\n\n\t},\n\t// 页面周期函数--监听页面隐藏\n\tonHide() { },\n\t// 页面周期函数--监听页面卸载\n\tonUnload() { },\n\t// 页面处理函数--监听用户上拉触底\n\tonReachBottom() { },\n\t// 页面处理函数--监听用户下拉动作\n\tonPullDownRefresh() { },\n\t// 页面周期函数--监听页面返回\n\tonBackPress() { },\n\t// 页面处理函数--监听页面滚动(not-nvue)\n\tonPageScroll(e) { },\n\t// 页面处理函数--用户点击右上角分享好友\n\tonShareAppMessage(options) { },\n\t// 页面处理函数--用户点击右上角分享朋友圈\n\tonShareTimeline(options) { }\n};\n</script>\n<style lang=\"scss\" scoped>\n.practise-content {\n\twidth: 100%;\n\tmin-height: 100vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground-image: linear-gradient(to bottom, #FFD101 10%, #FFF 50%);\n\n\t.u-nav-slot {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t.arrow {\n\t\t\twidth: 30rpx;\n\t\t\theight: 30rpx;\n\t\t}\n\n\t\t.label {\n\t\t\tfont-size: 36rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #000000;\n\t\t\tmargin: 0 15rpx 0 5rpx;\n\t\t}\n\n\t\t.value {\n\t\t\tfont-size: 30rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #000000;\n\t\t}\n\t}\n\n\t.practise-box {\n\t\twidth: 100%;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 43rpx 43rpx 0 0;\n\t\tmargin-top: 15rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 40rpx 25rpx;\n\n\t\t.practise-type {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 30rpx;\n\n\t\t\t.type {\n\t\t\t\theight: 58rpx;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #000000;\n\t\t\t\tline-height: 58rpx;\n\t\t\t\tpadding: 0 17rpx;\n\t\t\t\tbackground-color: #FFF8D8;\n\t\t\t\tborder-radius: 7rpx;\n\t\t\t\tborder: 2rpx solid #FFE674;\n\t\t\t\tmargin-right: 25rpx;\n\t\t\t}\n\n\t\t\t.schedule {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #464646;\n\t\t\t}\n\t\t}\n\n\t\t.content {\n\t\t\twidth: 100%;\n\t\t\tfont-size: 36rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #000000;\n\t\t\tline-height: 52rpx;\n\t\t\tmargin-bottom: 30rpx;\n\t\t}\n\n\t\t.options-list {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\n\t\t\t.options-li {\n\t\t\t\twidth: 100%;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tpadding: 25rpx 0;\n\t\t\t\tborder-radius: 20rpx;\n\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\n\t\t\t\t&:active {\n\t\t\t\t\t.option-content {\n\t\t\t\t\t\ttext-decoration: underline wavy #c0c4cc;\n\t\t\t\t\t}\n\n\t\t\t\t\tbackground-color: #f9f9f9;\n\t\t\t\t}\n\n\t\t\t\t&.option-correct {\n\t\t\t\t\t.option-label {\n\t\t\t\t\t\tbackground-color: #65BC17;\n\t\t\t\t\t\tborder-color: #65BC17;\n\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t}\n\n\t\t\t\t\t.option-content {\n\t\t\t\t\t\tcolor: #55B300;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.option-wrong {\n\n\t\t\t\t\t.option-label {\n\t\t\t\t\t\tbackground-color: #F4621A;\n\t\t\t\t\t\tborder-color: #F4621A;\n\t\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\t}\n\n\t\t\t\t\t// .option-content {\n\t\t\t\t\t// \tcolor: #F44336;\n\t\t\t\t\t// }\n\t\t\t\t}\n\n\t\t\t\t.option-label {\n\t\t\t\t\twidth: 50rpx;\n\t\t\t\t\theight: 50rpx;\n\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\tline-height: 50rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tbox-sizing: border-box;\n\t\t\t\t\tcolor: #000000;\n\t\t\t\t\tborder-radius: 46rpx;\n\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\tbackground-color: #FFF;\n\t\t\t\t\tborder: 2rpx solid #B2B2B2;\n\t\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\t\t\t\t}\n\n\t\t\t\t.option-content {\n\t\t\t\t\twidth: 630rpx;\n\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\tcolor: #464646;\n\t\t\t\t\tline-height: 50rpx;\n\t\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.correct-answer {\n\t\t\twidth: 100%;\n\t\t\theight: 86rpx;\n\t\t\tbackground-color: #E9F8D1;\n\t\t\tborder-radius: 10rpx;\n\t\t\tpadding: 0 30rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmargin: 40rpx 0;\n\n\t\t\t.label {\n\t\t\t\tfont-size: 36rpx;\n\t\t\t\tline-height: 86rpx;\n\t\t\t\tcolor: #459101;\n\t\t\t}\n\n\t\t\t.value {\n\t\t\t\tfont-size: 36rpx;\n\t\t\t\tline-height: 40rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #459101;\n\t\t\t}\n\t\t}\n\t}\n\n\t.answer-analysis {\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 20rpx 25rpx;\n\n\t\t.analysis-title {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 35rpx;\n\n\t\t\t.title {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #000000;\n\t\t\t\tz-index: 10;\n\t\t\t}\n\n\t\t\t.icon {\n\t\t\t\twidth: 126rpx;\n\t\t\t\theight: 15rpx;\n\t\t\t\tbackground-image: linear-gradient(to right, #FFD101 0%, #FFFFFF 100%);\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tposition: relative;\n\t\t\t\tbottom: 10rpx;\n\t\t\t}\n\t\t}\n\n\t\t.analysis-content {\n\t\t\tfont-size: 36rpx;\n\t\t\tcolor: #000000;\n\t\t\tline-height: 52rpx;\n\t\t}\n\t}\n\n\t.feedback {\n\t\tposition: fixed;\n\t\tleft: 25rpx;\n\t\tbottom: 240rpx;\n\t\twidth: 68rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-direction: column;\n\t\ttransition: all 100ms cubic-bezier(.36, .66, .04, 1);\n\n\t\t&:active {\n\t\t\ttransform: scale(0.99);\n\t\t}\n\n\t\t.icon {\n\t\t\twidth: 68rpx;\n\t\t\theight: 62rpx;\n\t\t}\n\n\t\t.text {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #464646;\n\t\t}\n\t}\n\n\t.position-bottom {\n\t\tposition: fixed;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tpadding: 25rpx 20rpx;\n\t\tbackground-color: #FFF;\n\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);\n\t\tborder-top: 1rpx solid #e4e7ed;\n\n\t\t.button-list {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\n\t\t\t.button {\n\t\t\t\twidth: 186rpx;\n\t\t\t\theight: 80rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tbackground-color: #DDDDDD;\n\t\t\t\tborder-radius: 21rpx;\n\t\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\n\t\t\t\tmargin: 0;\n\n\t\t\t\t.icon {\n\t\t\t\t\twidth: 50rpx;\n\t\t\t\t\theight: 50rpx;\n\t\t\t\t\tmargin-right: 10rpx;\n\t\t\t\t}\n\n\t\t\t\t.text {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #B2B2B2;\n\t\t\t\t}\n\n\t\t\t\t.value {\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #464646;\n\t\t\t\t}\n\n\t\t\t\t&.button-s {\n\t\t\t\t\tbackground-color: #FFD101;\n\t\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);\n\n\t\t\t\t\t.text {\n\t\t\t\t\t\tcolor: #000;\n\t\t\t\t\t}\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\tbackground-color: #f7ca00;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.next-step {\n\t\t\t\t\tmin-width: 272rpx;\n\t\t\t\t\tpadding: 0 3rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=style&index=0&id=59d936fe&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./practise.vue?vue&type=style&index=0&id=59d936fe&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749956210583\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}