.flex.data-v-0caffd1d {
  display: flex;
}
.column.data-v-0caffd1d {
  flex-direction: column;
}
.center.data-v-0caffd1d {
  align-items: center;
}
.space-between.data-v-0caffd1d {
  justify-content: space-between;
}
.success-content.data-v-0caffd1d {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}
.success-content .header.data-v-0caffd1d {
  width: 100%;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(to bottom, #FFD101, #FFFFFF);
}
.success-content .header .title.data-v-0caffd1d {
  font-size: 60rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}
.success-content .score-section.data-v-0caffd1d {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120rpx;
}
.success-content .progress-container.data-v-0caffd1d {
  margin-bottom: 80rpx;
  overflow: hidden;
}
.success-content .progress-circle.data-v-0caffd1d {
  position: relative;
  width: 416rpx;
  height: 208rpx;
}
.success-content .progress-circle .progress-bg.data-v-0caffd1d {
  position: absolute;
  top: 0;
  left: 0;
  width: 416rpx;
  height: 416rpx;
  border: 20rpx solid #FFED9C;
  border-radius: 50%;
  border-bottom: 20rpx solid transparent;
  clip: rect(0, 416rpx, 208rpx, 0);
}
.success-content .progress-circle .progress-bar.data-v-0caffd1d {
  position: absolute;
  top: 0;
  left: 0;
  width: 416rpx;
  height: 416rpx;
  border: 20rpx solid #FFD101;
  border-radius: 50%;
  border-bottom: 20rpx solid transparent;
  clip: rect(0, 416rpx, 208rpx, 0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
  transition: -webkit-transform 0.1s ease-out;
  transition: transform 0.1s ease-out;
  transition: transform 0.1s ease-out, -webkit-transform 0.1s ease-out;
}
.success-content .progress-circle .progress-dot.data-v-0caffd1d {
  position: absolute;
  top: 10rpx;
  right: 198rpx;
  width: 20rpx;
  height: 20rpx;
  background-color: #FFD101;
  border-radius: 50%;
  -webkit-transform-origin: 198rpx 198rpx;
          transform-origin: 198rpx 198rpx;
  transition: -webkit-transform 0.1s ease-out;
  transition: transform 0.1s ease-out;
  transition: transform 0.1s ease-out, -webkit-transform 0.1s ease-out;
  z-index: 5;
}
.success-content .progress-circle .progress-center.data-v-0caffd1d {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -20%);
          transform: translate(-50%, -20%);
  text-align: center;
  z-index: 10;
}
.success-content .progress-circle .progress-center .score-text.data-v-0caffd1d {
  display: block;
  font-size: 80rpx;
  font-weight: bold;
  color: #FFD101;
  line-height: 1;
  margin-bottom: 10rpx;
}
.success-content .progress-circle .progress-center .score-label.data-v-0caffd1d {
  display: block;
  font-size: 32rpx;
  color: #666;
}
.success-content .progress-container.data-v-0caffd1d {
  position: relative;
}
.success-content .progress-container.data-v-0caffd1d::before {
  content: "0";
  position: absolute;
  bottom: -40rpx;
  left: 20rpx;
  font-size: 28rpx;
  color: #999;
}
.success-content .progress-container.data-v-0caffd1d::after {
  content: "100";
  position: absolute;
  bottom: -40rpx;
  right: 20rpx;
  font-size: 28rpx;
  color: #999;
}
.success-content .stats-container.data-v-0caffd1d {
  display: flex;
  justify-content: space-around;
  width: 100%;
  max-width: 600rpx;
}
.success-content .stats-container .stats-item.data-v-0caffd1d {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.success-content .stats-container .stats-item .stats-number.data-v-0caffd1d {
  font-size: 60rpx;
  font-weight: bold;
  color: #FFD101;
  line-height: 1;
  margin-bottom: 10rpx;
}
.success-content .stats-container .stats-item .stats-number.correct.data-v-0caffd1d {
  color: #4CAF50;
}
.success-content .stats-container .stats-item .stats-number.wrong.data-v-0caffd1d {
  color: #FF5722;
}
.success-content .stats-container .stats-item .stats-label.data-v-0caffd1d {
  font-size: 28rpx;
  color: #999;
}
.success-content .action-buttons.data-v-0caffd1d {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 600rpx;
  gap: 30rpx;
}
.success-content .action-buttons .btn.data-v-0caffd1d {
  width: 100%;
  height: 100rpx;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}
.success-content .action-buttons .btn.btn-primary.data-v-0caffd1d {
  background: linear-gradient(135deg, #FFD101 0%, #FFA000 100%);
  color: #333;
  box-shadow: 0rpx 8rpx 20rpx rgba(255, 209, 1, 0.3);
}
.success-content .action-buttons .btn.btn-primary.data-v-0caffd1d:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0rpx 4rpx 10rpx rgba(255, 209, 1, 0.3);
}
.success-content .action-buttons .btn.btn-secondary.data-v-0caffd1d {
  background: #FFF;
  color: #FFD101;
  border: 2rpx solid #FFD101;
}
.success-content .action-buttons .btn.btn-secondary.data-v-0caffd1d:active {
  background: #FFF8E1;
}
