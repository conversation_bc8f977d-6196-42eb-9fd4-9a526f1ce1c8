.flex.data-v-0caffd1d {
  display: flex;
}
.column.data-v-0caffd1d {
  flex-direction: column;
}
.center.data-v-0caffd1d {
  align-items: center;
}
.space-between.data-v-0caffd1d {
  justify-content: space-between;
}
.success-content.data-v-0caffd1d {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}
.success-content .header.data-v-0caffd1d {
  width: 100%;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(to bottom, #FFD101, #FFFFFF);
}
.success-content .header .title.data-v-0caffd1d {
  font-size: 60rpx;
  font-weight: bold;
  color: #000;
  text-align: center;
}
.success-content .score-section.data-v-0caffd1d {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.success-content .progress-container.data-v-0caffd1d {
  overflow: hidden;
}
.success-content .progress-circle.data-v-0caffd1d {
  position: relative;
  width: 416rpx;
  height: 208rpx;
}
.success-content .progress-circle .progress-bg.data-v-0caffd1d {
  position: absolute;
  top: 0;
  left: 0;
  width: 416rpx;
  height: 416rpx;
  border: 20rpx solid #FFD101;
  border-radius: 50%;
  border-bottom: 20rpx solid transparent;
  clip: rect(0, 416rpx, 208rpx, 0);
}
.success-content .progress-circle .progress-bar.data-v-0caffd1d {
  position: absolute;
  top: 0;
  left: 0;
  width: 416rpx;
  height: 416rpx;
  border: 20rpx solid #FFED9C;
  border-radius: 50%;
  border-bottom: 20rpx solid transparent;
  clip: rect(0, 416rpx, 208rpx, 0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg);
  transition: -webkit-transform 0.1s ease-out;
  transition: transform 0.1s ease-out;
  transition: transform 0.1s ease-out, -webkit-transform 0.1s ease-out;
}
.success-content .progress-circle .progress-center.data-v-0caffd1d {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -25%);
          transform: translate(-50%, -25%);
  text-align: center;
  z-index: 10;
}
.success-content .progress-circle .progress-center .score-text.data-v-0caffd1d {
  display: block;
  font-size: 72rpx;
  font-weight: bold;
  color: #F1B500;
  line-height: 1;
  margin-bottom: 10rpx;
}
.success-content .progress-circle .progress-center .score-label.data-v-0caffd1d {
  display: block;
  font-size: 36rpx;
  color: #464646;
}
.success-content .progress-scope.data-v-0caffd1d {
  position: absolute;
  top: 215rpx;
  left: 57rpx;
  width: 427rpx;
  display: flex;
  justify-content: space-between;
  z-index: 10;
}
.success-content .progress-scope .text.data-v-0caffd1d {
  font-size: 24rpx;
  color: #464646;
}
.success-content .stats-container.data-v-0caffd1d {
  display: flex;
  justify-content: space-between;
  width: 530rpx;
  margin-top: 130rpx;
}
.success-content .stats-container .stats-item.data-v-0caffd1d {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.success-content .stats-container .stats-item .stats-number.data-v-0caffd1d {
  font-size: 56rpx;
  font-weight: bold;
  color: #F1B500;
  line-height: 1;
  margin-bottom: 18rpx;
}
.success-content .stats-container .stats-item .stats-number.correct.data-v-0caffd1d {
  color: #19be6b;
}
.success-content .stats-container .stats-item .stats-number.wrong.data-v-0caffd1d {
  color: #fa3534;
}
.success-content .stats-container .stats-item .stats-label.data-v-0caffd1d {
  font-size: 30rpx;
  color: #464646;
}
.success-content .action-buttons.data-v-0caffd1d {
  display: flex;
  flex-direction: column;
  width: 534rpx;
  gap: 63rpx;
  margin-top: 115rpx;
}
.success-content .action-buttons .btn.data-v-0caffd1d {
  width: 100%;
  height: 108rpx;
  border-radius: 21rpx;
  font-size: 36rpx;
  color: #000000;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.success-content .action-buttons .btn.btn-primary.data-v-0caffd1d {
  background-color: #FFD101;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);
}
.success-content .action-buttons .btn.btn-primary.data-v-0caffd1d:active {
  background-color: #e7bd00;
}
