{"version": 3, "sources": ["webpack:///E:/KingToyo/app_tixiaomeng/components/quickLogin.vue?6e70", "webpack:///E:/KingToyo/app_tixiaomeng/components/quickLogin.vue?10d6", "webpack:///E:/KingToyo/app_tixiaomeng/components/quickLogin.vue?8df4", "webpack:///E:/KingToyo/app_tixiaomeng/components/quickLogin.vue?09da", "uni-app:///components/quickLogin.vue", "webpack:///E:/KingToyo/app_tixiaomeng/components/quickLogin.vue?e66b", "webpack:///E:/KingToyo/app_tixiaomeng/components/quickLogin.vue?e777"], "names": ["data", "showPopup", "selected", "phoneNumber", "computed", "methods", "show", "close", "onSelected", "uni", "icon", "title", "getrealtimephonenumber", "that", "duration", "e", "params", "code", "wxBindOpenId", "openId", "checkProtocol", "url", "watch", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onPullDownRefresh", "onReachBottom", "onBackPress"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzCA;AAAA;AAAA;AAAA;AAA+qB,CAAgB,osBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACoBnsB;AACA;;;;;;;;;;;;;;;;;;;;eACA;EACAA;IAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EAAA;;EACAC;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAC;QACAC;QACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAJ;kBACAC;kBACAC;kBACAG;gBACA;gBAAA,iCACA;cAAA;gBAAA,MAEAC;kBAAA;kBAAA;gBAAA;gBACAN;gBACAO;gBACAA;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAAC;gBAAAjB;gBACA;kBACA;oBACAa;oBACAA;oBACAA;oBACA;oBACA;oBACA;oBACAA;oBACAA;oBACAJ;sBACAC;sBACAC;sBACAG;oBACA;oBACAD;kBACA;oBACAJ;sBACAC;sBACAC;sBACAG;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAL;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAO;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;gBACA;cAAA;gBAAA;gBAFAF;gBAAAjB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACA;IACAoB;MACA;QACAX;UACAY;QACA;MACA;QACAZ;UACAY;QACA;MACA;IACA;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC,uCACA;EACA;EACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvIA;AAAA;AAAA;AAAA;AAA8yC,CAAgB,2wCAAG,EAAC,C;;;;;;;;;;;ACAl0C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/quickLogin.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./quickLogin.vue?vue&type=template&id=85710212&scoped=true&\"\nvar renderjs\nimport script from \"./quickLogin.vue?vue&type=script&lang=js&\"\nexport * from \"./quickLogin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./quickLogin.vue?vue&type=style&index=0&id=85710212&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"85710212\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/quickLogin.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./quickLogin.vue?vue&type=template&id=85710212&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.selected = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.selected = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./quickLogin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./quickLogin.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"column-centent\">\r\n\t\t<u-popup :show=\"showPopup\" :round=\"10\" :safeAreaInsetBottom=\"false\" mode=\"bottom\" closeable @close=\"close\">\r\n\t\t\t<view class=\"login-content\">\r\n\t\t\t\t<button class=\"u-reset-button item-button-n\" v-if=\"!selected\" @click=\"onSelected\">授权一键登录</button>\r\n\t\t\t\t<button class=\"u-reset-button item-button-s\" v-else open-type=\"getPhoneNumber\" @getphonenumber=\"getrealtimephonenumber\">授权一键登录</button>\r\n\t\t\t\t<view class=\"protocol-privacy\">\r\n\t\t\t\t\t<image class=\"image\" v-if=\"selected\" @tap=\"selected = false\" src=\"@/static/images/agree-icon.png\" />\r\n\t\t\t\t\t<image class=\"image\" v-if=\"!selected\" @tap=\"selected = true\" src=\"@/static/images/disagree-icon.png\" />\r\n\t\t\t\t\t<text class=\"text\">我已阅读并同意</text>\r\n\t\t\t\t\t<text class=\"text\" @tap=\"checkProtocol(1)\">《用户协议》</text>\r\n\t\t\t\t\t<text class=\"text\">和</text>\r\n\t\t\t\t\t<text class=\"text\" @tap=\"checkProtocol(2)\">《隐私政策》</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { wxGetToken, bindOpenId } from '@/api/login'\r\nimport store from '@/store'\r\nexport default {\r\n  data: () => ({\r\n\t\tshowPopup: false, // 弹窗状态\r\n\t\tselected: false, // 选中协议\r\n\t\tphoneNumber: '', // 手机号\r\n  }),\r\n  computed: {},\r\n  methods: {\r\n\t\t// 显示Popup弹出层\r\n\t\tshow() {\r\n\t\t  this.showPopup = true\r\n\t\t},\r\n\t\t// 关闭Popup弹出层\r\n\t\tclose() {\r\n\t\t  this.showPopup = false\r\n\t\t},\r\n\t\t// 请选择协议\r\n\t\tonSelected() {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ticon: \"none\",\r\n\t\t\t\ttitle: \"请确认并勾选下方《用户协议》和《隐私政策》\"\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 获取手机号\r\n\t\tasync getrealtimephonenumber(e) {\r\n\t\t\tlet that = this\r\n\t\t\tif (!that.selected) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\ttitle: \"请确认并勾选下方《用户协议》和《隐私政策》\",\r\n\t\t\t\t\tduration: 1500\r\n\t\t\t\t})\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t\tif (e.detail.errMsg == 'getPhoneNumber:ok') {\r\n\t\t\t\tuni.showLoading()\r\n\t\t\t\tlet params = new Object()\r\n\t\t\t\tparams.code = e.detail.code\r\n\t\t\t\tif (store.state.user.invitationCode) {\r\n\t\t\t\t\tparams.invitationCode = store.state.user.invitationCode\r\n\t\t\t\t}\r\n\t\t\t\tconst { code, data } = await wxGetToken(params)\r\n\t\t\t\tif (code == '00000') {\r\n\t\t\t\t\tif (data.token) {\r\n\t\t\t\t\t\tthat.$store.dispatch(\"SetToken\", data.token)\r\n\t\t\t\t\t\tthat.$store.dispatch(\"SetUserId\", data.userId)\r\n\t\t\t\t\t\tthat.$store.dispatch(\"SetPhone\", data.phone)\r\n\t\t\t\t\t\t// that.$store.dispatch(\"SetPhone\", '18080809975')\r\n\t\t\t\t\t\tthis.$store.dispatch(\"SetInvitationCode\", \"\")\r\n\t\t\t\t\t\t// 绑定openId\r\n\t\t\t\t\t\tthat.wxBindOpenId()\r\n\t\t\t\t\t\tthat.$emit(\"loginSuccess\", data)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthat.close()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\ttitle: '返回为空~',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '获取失败，请重试~'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 绑定openid到账户\r\n\t\tasync wxBindOpenId() {\r\n\t\t\tconst { code, data } = await bindOpenId({\r\n\t\t\t\topenId: store.state.user.openId\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 用户协议、隐私政策\r\n\t\tcheckProtocol(e) {\r\n\t\t\tif (e == 1) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/pages/mine/rule?type=1\"\r\n\t\t\t\t})\r\n\t\t\t} else if (e == 2) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/pages/mine/rule?type=2\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t},\r\n  watch: {},\r\n  // 页面周期函数--监听页面加载\r\n  onLoad(options) {},\r\n  // 页面周期函数--监听页面初次渲染完成\r\n  onReady() {},\r\n  // 页面周期函数--监听页面显示(not-nvue)\r\n  onShow() {},\r\n  // 页面周期函数--监听页面隐藏\r\n  onHide() {},\r\n  // 页面周期函数--监听页面卸载\r\n  onUnload() {},\r\n  // 页面处理函数--监听用户下拉动作\r\n  onPullDownRefresh() {},\r\n  // 页面处理函数--监听用户上拉触底\r\n  onReachBottom() {},\r\n\t// 页面周期函数--监听页面返回\r\n\tonBackPress() {},\r\n  // 页面处理函数--监听页面滚动(not-nvue)\r\n  /* onPageScroll(event) {}, */\r\n  // 页面处理函数--用户点击右上角分享\r\n  /* onShareAppMessage(options) {}, */\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.column-centent {\r\n\twidth: 100%;\r\n\t.login-content {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-color: #F3F4F6;\r\n\t\tpadding: 135rpx 25rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\t.item-button-n {\r\n\t\t\twidth: 500rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\tcolor: #FFF;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tfont-family: PingFang SC;\r\n\t\t\tbackground-color: #909399;\r\n\t\t\ttransition: all 100ms cubic-bezier(.36,.66,.04,1);\r\n\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(144,147,153,0.2), 0rpx 6rpx 16rpx 0rpx rgba(144,147,153,0.2);\r\n\t\t}\r\n\t\t.item-button-s {\r\n\t\t\twidth: 500rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\tcolor: #FFF;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tfont-family: PingFang SC;\r\n\t\t\tbackground-color: #2ED573;\r\n\t\t\ttransition: all 100ms cubic-bezier(.36,.66,.04,1);\r\n\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(46,213,115,0.2), 0rpx 6rpx 16rpx 0rpx rgba(46,213,115,0.2);\r\n\t\t\t&:active {\r\n\t\t\t\tbackground-color: #25ab5a;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.protocol-privacy {\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmargin-top: 60rpx;\r\n\t\t\tmargin-bottom: 80rpx;\r\n\t\t\t.image {\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\t\t\t.text {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #111111;\r\n\t\t\t\t&:nth-child(3) {\r\n\t\t\t\t\tcolor: #2558B3;\r\n\t\t\t\t}\r\n\t\t\t\t&:nth-child(5) {\r\n\t\t\t\t\tcolor: #2558B3;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./quickLogin.vue?vue&type=style&index=0&id=85710212&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./quickLogin.vue?vue&type=style&index=0&id=85710212&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749956143946\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}