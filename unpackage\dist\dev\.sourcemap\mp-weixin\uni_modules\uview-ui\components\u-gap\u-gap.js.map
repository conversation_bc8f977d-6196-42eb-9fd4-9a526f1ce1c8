{"version": 3, "sources": ["webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-gap/u-gap.vue?ccc7", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-gap/u-gap.vue?cc42", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-gap/u-gap.vue?d0ba", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-gap/u-gap.vue?b0c2", "uni-app:///uni_modules/uview-ui/components/u-gap/u-gap.vue", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-gap/u-gap.vue?64bd", "webpack:///E:/KingToyo/app_tixiaomeng/uni_modules/uview-ui/components/u-gap/u-gap.vue?7e59"], "names": ["name", "mixins", "computed", "gapStyle", "backgroundColor", "height", "marginTop", "marginBottom"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACK9rB;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,eAYA;EACAA;EACAC;EACAC;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChCA;AAAA;AAAA;AAAA;AAAyyC,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACA7zC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-gap/u-gap.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-gap.vue?vue&type=template&id=b138c5b2&scoped=true&\"\nvar renderjs\nimport script from \"./u-gap.vue?vue&type=script&lang=js&\"\nexport * from \"./u-gap.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-gap.vue?vue&type=style&index=0&id=b138c5b2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b138c5b2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-gap/u-gap.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-gap.vue?vue&type=template&id=b138c5b2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.gapStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-gap.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-gap.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-gap\" :style=\"[gapStyle]\"></view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * gap 间隔槽\r\n\t * @description 该组件一般用于内容块之间的用一个灰色块隔开的场景，方便用户风格统一，减少工作量\r\n\t * @tutorial https://www.uviewui.com/components/gap.html\r\n\t * @property {String}\t\t\tbgColor\t\t\t背景颜色 （默认 'transparent' ）\r\n\t * @property {String | Number}\theight\t\t\t分割槽高度，单位px （默认 20 ）\r\n\t * @property {String | Number}\tmarginTop\t\t与前一个组件的距离，单位px（ 默认 0 ）\r\n\t * @property {String | Number}\tmarginBottom\t与后一个组件的距离，单位px （默认 0 ）\r\n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\r\n\t * \r\n\t * @example <u-gap height=\"80\" bg-color=\"#bbb\"></u-gap>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-gap\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\tcomputed: {\r\n\t\t\tgapStyle() {\r\n\t\t\t\tconst style = {\r\n\t\t\t\t\tbackgroundColor: this.bgColor,\r\n\t\t\t\t\theight: uni.$u.addUnit(this.height),\r\n\t\t\t\t\tmarginTop: uni.$u.addUnit(this.marginTop),\r\n\t\t\t\t\tmarginBottom: uni.$u.addUnit(this.marginBottom),\r\n\t\t\t\t}\r\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n</style>\r\n", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-gap.vue?vue&type=style&index=0&id=b138c5b2&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-gap.vue?vue&type=style&index=0&id=b138c5b2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749918813437\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}