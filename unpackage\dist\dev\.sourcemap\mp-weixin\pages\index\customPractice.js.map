{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/customPractice.vue?7cd8", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/customPractice.vue?3f29", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/customPractice.vue?ea50", "uni-app:///pages/index/customPractice.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/customPractice.vue?5fe4", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/customPractice.vue?96f5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "statusBarHeight", "questionList", "Array", "length", "id", "name", "selected", "isAllSelected", "isNextStep", "filters", "methods", "toggleQuestion", "toggleAllSelect", "item", "cancelAllSelect", "updateAllSelectedState", "onAnswer", "uni", "url", "openPrivacyContract", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onReachBottom", "onPullDownRefresh", "onBackPress", "onPageScroll", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,2UAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAmrB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8CvsB;EACAC;EACAC;IAAA;MACAC;MAAA;MACA;MACAC,+CACAC;QAAAC;MAAA;QAAA;UACAC;UACAC;UACAC;QACA;MAAA,GACA;MACAC;MAAA;MACAC;IACA;EAAA;EACAC;EACAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACAD;MACA;MACA;IACA;IAEA;IACAE;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MACA;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACAF;IACA;EAEA;EACA;EACAG,kCAEA;EACA;EACAC;EACA;EACAC;IACA;IACA;EACA;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;ACnIA;AAAA;AAAA;AAAA;AAAkzC,CAAgB,+wCAAG,EAAC,C;;;;;;;;;;;ACAt0C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/customPractice.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/customPractice.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./customPractice.vue?vue&type=template&id=1d6b1bbc&scoped=true&\"\nvar renderjs\nimport script from \"./customPractice.vue?vue&type=script&lang=js&\"\nexport * from \"./customPractice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./customPractice.vue?vue&type=style&index=0&id=1d6b1bbc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1d6b1bbc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/customPractice.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./customPractice.vue?vue&type=template&id=1d6b1bbc&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-navbar/u-navbar\" */ \"@/uni_modules/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uSticky: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-sticky/u-sticky\" */ \"@/uni_modules/uview-ui/components/u-sticky/u-sticky.vue\"\n      )\n    },\n    uSafeBottom: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom\" */ \"@/uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./customPractice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./customPractice.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"select-content\">\r\n\t\t<u-navbar :placeholder=\"true\"  bgColor=\"#FFD101\" :autoBack=\"true\">\r\n\t\t\t<view class=\"u-nav-slot\" slot=\"left\">\r\n\t\t\t\t<image class=\"arrow\" src=\"@/static/images/arrow-left.png\" /> L系列\r\n\t\t\t</view>\r\n\t\t</u-navbar>\r\n\t\t<u-sticky bgColor=\"transparent\" :customNavHeight=\"44 + statusBarHeight\">\r\n\t\t\t<view class=\"header-content\">\r\n\t\t\t\t<view class=\"header-box\">\r\n\t\t\t\t\t<view class=\"left-item\" @click=\"toggleAllSelect\">\r\n\t\t\t\t\t\t<image class=\"icon\" v-if=\"isAllSelected\" src=\"@/static/images/agree-icon.png\" />\r\n\t\t\t\t\t\t\t<image class=\"icon\" v-else src=\"@/static/images/disagree-icon.png\" />\r\n\t\t\t\t\t\t<text class=\"text\">全选</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"u-reset-button right-item\" :class=\"{'right-item-s': isAllSelected}\" @click=\"cancelAllSelect\">取消选择</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-sticky>\r\n\t\t<view class=\"select-list\">\r\n\t\t\t<view class=\"select-li\" :class=\"{'select-li-s': item.selected}\" v-for=\"(item, index) in questionList\" :key=\"item.id\" @click=\"toggleQuestion(index)\">\r\n\t\t\t\t<text class=\"value\">{{ item.name }}</text>\r\n\t\t\t\t<image class=\"icon\" v-if=\"item.selected\" src=\"@/static/images/select-s.png\" />\r\n\t\t\t\t<image class=\"icon\" v-else src=\"@/static/images/select-n.png\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"placeholder\"></view>\r\n\t\t\t<view class=\"placeholder\"></view>\r\n\t\t</view>\r\n\t\t<u-safe-bottom />\r\n\t\t<view class=\"position-bottom\">\r\n\t\t\t<view class=\"button-list\">\r\n\t\t\t\t<button class=\"u-reset-button button\" :class=\"{'button-s': isNextStep}\">\r\n\t\t\t\t\t<image class=\"icon\" src=\"@/static/images/answer-icon-3.png\" />\r\n\t\t\t\t\t<text class=\"text\">顺序练习</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t<button class=\"u-reset-button button\" :class=\"{'button-s': isNextStep}\">\r\n\t\t\t\t\t<image class=\"icon\" src=\"@/static/images/answer-icon-4.png\" />\r\n\t\t\t\t\t<text class=\"text\">顺序练习</text>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t\t<u-safe-bottom />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tcomponents: {},\r\n\tdata: () => ({\r\n\t\tstatusBarHeight: 0, // 状态栏高度\r\n\t\t// 题目列表数据\r\n\t\tquestionList: [\r\n\t\t\t...Array.from({ length: 100 }, (_, index) => ({\r\n\t\t\t\tid: index + 3,\r\n\t\t\t\tname: `1-${index + 3}`,\r\n\t\t\t\tselected: false\r\n\t\t\t}))\r\n\t\t],\r\n\t\tisAllSelected: false, // 全选状态\r\n\t\tisNextStep: false,\r\n\t}),\r\n\tfilters: {},\r\n\tmethods: {\r\n\t\t// 切换单个题目选中状态\r\n\t\ttoggleQuestion(index) {\r\n\t\t\tthis.questionList[index].selected = !this.questionList[index].selected;\r\n\t\t\tthis.updateAllSelectedState();\r\n\t\t},\r\n\r\n\t\t// 全选/取消全选\r\n\t\ttoggleAllSelect() {\r\n\t\t\tthis.isAllSelected = !this.isAllSelected;\r\n\t\t\tthis.questionList.forEach(item => {\r\n\t\t\t\titem.selected = this.isAllSelected;\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 取消所有选择\r\n\t\tcancelAllSelect() {\r\n\t\t\tthis.questionList.forEach(item => {\r\n\t\t\t\titem.selected = false;\r\n\t\t\t});\r\n\t\t\tthis.isAllSelected = false;\r\n\t\t},\r\n\r\n\t\t// 更新全选状态\r\n\t\tupdateAllSelectedState() {\r\n\t\t\tconst selectedCount = this.questionList.filter(item => item.selected).length;\r\n\t\t\tthis.isAllSelected = selectedCount === this.questionList.length;\r\n\t\t},\r\n\r\n\t\t// 答题\r\n\t\tonAnswer() {\r\n\t\t\t// 跳转至答题页面\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/index/answer'\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 跳转至隐私协议页面\r\n\t\topenPrivacyContract() {\r\n\t\t\tuni.openPrivacyContract()\r\n\t\t},\r\n\r\n\t},\r\n\t// 页面周期函数--监听页面加载\r\n\tonLoad(options) {\r\n\r\n\t},\r\n\t// 页面周期函数--监听页面初次渲染完成\r\n\tonReady() {\t\t},\r\n\t// 页面周期函数--监听页面显示(not-nvue)\r\n\tonShow() {\r\n\t\t// 状态栏高度\r\n\t\tthis.statusBarHeight = this.$store.state.app.statusBarHeight\r\n\t},\r\n\t// 页面周期函数--监听页面隐藏\r\n\tonHide() { },\r\n\t// 页面周期函数--监听页面卸载\r\n\tonUnload() { },\r\n\t// 页面处理函数--监听用户上拉触底\r\n\tonReachBottom() { },\r\n\t// 页面处理函数--监听用户下拉动作\r\n\tonPullDownRefresh() { },\r\n\t// 页面周期函数--监听页面返回\r\n\tonBackPress() { },\r\n\t// 页面处理函数--监听页面滚动(not-nvue)\r\n\tonPageScroll(e) { },\r\n\t// 页面处理函数--用户点击右上角分享好友\r\n\tonShareAppMessage(options) { },\r\n\t// 页面处理函数--用户点击右上角分享朋友圈\r\n\tonShareTimeline(options) { }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.select-content {\r\n\twidth: 100%;\r\n\tmin-height: 100vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tbackground-image: linear-gradient(to bottom, #FFF3E0 10%, #F7F7F7 50%);\r\n\r\n\t.u-nav-slot {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #000000;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\t.arrow {\r\n\t\t\twidth: 30rpx;\r\n\t\t\theight: 30rpx;\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t}\r\n\t}\r\n\t.header-content {\r\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\r\n\t\tbackground-color: #FFD101;\r\n\t\tpadding-top: 15rpx;\n\t}\r\n\t.header-box {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 22rpx 25rpx;\r\n\t\tbackground-color: #FFF3E0;\r\n\t\tborder-radius: 43rpx 43rpx 0 0;\r\n\t\t.left-item {\r\n\t\t\theight: 48rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.icon {\r\n\t\t\t\twidth: 35rpx;\r\n\t\t\t\theight: 35rpx;\r\n\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.text {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tline-height: 48rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.right-item {\r\n\t\t\twidth: 182rpx;\r\n\t\t\theight: 48rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #B2B2B2;\r\n\t\t\tbackground-color: #DDDDDD;\r\n\t\t\tborder-radius: 24rpx;\r\n\t\t\tmargin: 0;\r\n\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\r\n\t\t\t&.right-item-s {\r\n\t\t\t\tbackground-color: #FFD101;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.select-list {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 16rpx 25rpx;\r\n\t\tbackground-color: #F7F7F7;\r\n\t\tborder-radius: 43rpx 43rpx 0 0;\r\n\t\tmargin-bottom: 180rpx;\r\n\t\t.placeholder {\r\n\t\t\twidth: 160rpx;\r\n\t\t\theight: 0rpx;\r\n\t\t}\r\n\r\n\t\t.select-li {\r\n\t\t\twidth: 160rpx;\r\n\t\t\theight: 110rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tmargin-bottom: 11rpx;\r\n\t\t\tposition: relative;\r\n\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);\r\n\t\t\ttransition: all 150ms cubic-bezier(.36, .66, .04, 1);\r\n\t\t\t&.select-li-s {\r\n\t\t\t\tbackground-color: #FFD101;\r\n\t\t\t}\r\n\t\t\t.value {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.icon {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 10rpx;\r\n\t\t\t\tright: 10rpx;\r\n\t\t\t\twidth: 33rpx;\r\n\t\t\t\theight: 33rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.position-bottom {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\n\t\tpadding: 25rpx 20rpx;\n\t\tbackground-color: #FFF;\n\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);\n\t\t\r\n\t\t.button-list {\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\n\t\t\t.button {\n\t\t\t\twidth: 338rpx;\n\t\t\t\theight: 100rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\n\t\t\t\tbackground-color: #DDDDDD;\n\t\t\t\tborder-radius: 29rpx;\n\t\t\t\ttransition: all 100ms cubic-bezier(.36, .66, .04, 1);\n\t\t\t\t.icon {\n\t\t\t\t\twidth: 60rpx;\n\t\t\t\t\theight: 60rpx;\n\t\t\t\t\tmargin-right: 15rpx;\n\t\t\t\t}\n\t\t\t\t.text {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #B2B2B2;\n\t\t\t\t}\r\n\t\t\t\t&.button-s {\r\n\t\t\t\t\tbackground-color: #FFD101;\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\tbackground-color: #f7ca00;\r\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\r\n\t}\r\n}\r\n</style>", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./customPractice.vue?vue&type=style&index=0&id=1d6b1bbc&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./customPractice.vue?vue&type=style&index=0&id=1d6b1bbc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749911693163\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}