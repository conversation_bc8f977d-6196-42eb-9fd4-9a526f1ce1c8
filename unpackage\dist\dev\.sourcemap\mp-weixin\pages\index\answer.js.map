{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/answer.vue?a486", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/answer.vue?03dd", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/answer.vue?e148", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/answer.vue?c302", "uni-app:///pages/index/answer.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/answer.vue?b822", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/answer.vue?029e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "filters", "methods", "onFunction", "uni", "url", "openPrivacyContract", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onReachBottom", "onPullDownRefresh", "onBackPress", "onPageScroll", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA2qB,CAAgB,gsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+D/rB;EACAC;EACAC;IAAA,QAEA;EAAA;EACAC;EACAC;IACA;IACAC;MACA;QACA;UACA;UACAC;YACAC;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;UACA;QACA;UACA;UACAD;YACAC;UACA;UACA;MAAA;IAEA;IACA;IACAC;MACAF;IACA;EAEA;EACA;EACAG,kCAEA;EACA;EACAC;EACA;EACAC,2BAEA;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAA0yC,CAAgB,uwCAAG,EAAC,C;;;;;;;;;;;ACA9zC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/answer.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/answer.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./answer.vue?vue&type=template&id=6f48d1d8&scoped=true&\"\nvar renderjs\nimport script from \"./answer.vue?vue&type=script&lang=js&\"\nexport * from \"./answer.vue?vue&type=script&lang=js&\"\nimport style0 from \"./answer.vue?vue&type=style&index=0&id=6f48d1d8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6f48d1d8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/answer.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./answer.vue?vue&type=template&id=6f48d1d8&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-navbar/u-navbar\" */ \"@/uni_modules/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-image/u-image\" */ \"@/uni_modules/uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./answer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./answer.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"answer-content\">\r\n\t\t<u-navbar :placeholder=\"true\" bgColor=\"#FFD101\" :autoBack=\"true\">\r\n\t\t\t<view class=\"u-nav-slot\" slot=\"left\"><image class=\"arrow\" src=\"@/static/images/arrow-left.png\" /> L系列</view>\r\n\t\t</u-navbar>\r\n\t\t<view class=\"header-box\"></view>\r\n\t\t<view class=\"header-image\">\r\n\t\t\t<u-image width=\"100%\" height=\"215rpx\" radius=\"43rpx\" src=\"@/static/images/banner.png\" />\r\n\t\t</view>\r\n\t\t<view class=\"answer-title\">\r\n\t\t\t<text class=\"value\">L系列 - L1</text>\r\n\t\t\t<text class=\"all-answer\">共2105题</text>\r\n\t\t</view>\r\n\t\t<view class=\"answer-list\">\r\n\t\t\t<view class=\"answer-li\" @click=\"onFunction(1)\">\r\n\t\t\t\t<image class=\"icon\" src=\"@/static/images/answer-icon-1.png\" />\r\n\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t<text class=\"title\">错题本</text>\r\n\t\t\t\t\t<view class=\"schedule\">\r\n\t\t\t\t\t\t<text class=\"label\">共55题</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"answer-li\" @click=\"onFunction(2)\">\r\n\t\t\t\t<image class=\"icon\" src=\"@/static/images/answer-icon-2.png\" />\r\n\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t<text class=\"title\">我的收藏</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"answer-li\" @click=\"onFunction(3)\">\r\n\t\t\t\t<image class=\"icon\" src=\"@/static/images/answer-icon-3.png\" />\r\n\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t<text class=\"title\">顺序练习</text>\r\n\t\t\t\t\t<view class=\"schedule\">\r\n\t\t\t\t\t\t<text class=\"value\">455</text>\r\n\t\t\t\t\t\t<text class=\"label margin\">/</text>\r\n\t\t\t\t\t\t<text class=\"label\">2359</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"answer-li\" @click=\"onFunction(4)\">\r\n\t\t\t\t<image class=\"icon\" src=\"@/static/images/answer-icon-4.png\" />\r\n\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t<text class=\"title\">随机练习</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"answer-li\" @click=\"onFunction(5)\">\r\n\t\t\t\t<image class=\"icon\" src=\"@/static/images/answer-icon-5.png\" />\r\n\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t<text class=\"title\">自选练习</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"answer-li\" @click=\"onFunction(6)\">\r\n\t\t\t\t<image class=\"icon\" src=\"@/static/images/answer-icon-5.png\" />\r\n\t\t\t\t<view class=\"title-schedule\">\r\n\t\t\t\t\t<text class=\"title\">购买纸质版</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tcomponents: {},\r\n\t\tdata: () => ({\r\n\t\t\t\r\n\t\t}),\r\n\t\tfilters: {},\r\n\t\tmethods: {\r\n\t\t\t// 功能跳转\r\n\t\t\tonFunction(e) {\r\n\t\t\t\tswitch (e) {\n\t\t\t\t\tcase 1:\n\t\t\t\t\t\t// 错题本\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/wrongBook'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 2:\n\t\t\t\t\t\t// 我的收藏\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/myCollection'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 3:\n\t\t\t\t\t\t// 顺序练习\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/sequencePractice'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 4:\n\t\t\t\t\t\t// 随机练习\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/randomPractice'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 5:\n\t\t\t\t\t\t// 自选练习\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/customPractice'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 6:\n\t\t\t\t\t\t// 购买纸质版\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/pages/index/purchasePaperVersion'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 跳转至隐私协议页面\r\n\t\t\topenPrivacyContract() {\r\n\t\t\t\tuni.openPrivacyContract()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面加载\r\n\t\tonLoad(options) {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面初次渲染完成\r\n\t\tonReady() {},\r\n\t\t// 页面周期函数--监听页面显示(not-nvue)\r\n\t\tonShow() {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面隐藏\r\n\t\tonHide() {},\r\n\t\t// 页面周期函数--监听页面卸载\r\n\t\tonUnload() {},\r\n\t\t// 页面处理函数--监听用户上拉触底\r\n\t\tonReachBottom() {},\r\n\t\t// 页面处理函数--监听用户下拉动作\r\n\t\tonPullDownRefresh() {},\r\n\t\t// 页面周期函数--监听页面返回\r\n\t\tonBackPress() {},\r\n\t\t// 页面处理函数--监听页面滚动(not-nvue)\r\n\t\tonPageScroll(e) {},\r\n\t\t// 页面处理函数--用户点击右上角分享好友\r\n\t\tonShareAppMessage(options) {},\r\n\t\t// 页面处理函数--用户点击右上角分享朋友圈\r\n\t\tonShareTimeline(options) {}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.answer-content {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 100vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbackground-color: #F7F7F7;\r\n\t\t.u-nav-slot {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #000000;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t.arrow {\r\n\t\t\t\twidth: 30rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.header-box {\n\t\t\twidth: 100%;\n\t\t\theight: 50rpx;\n\t\t\tbackground-color: #FFD101;\n\t\t}\r\n\t\t.header-image {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 215rpx;\r\n\t\t\tborder-radius: 43rpx;\r\n\t\t\tposition: relative;\r\n\t\t\tbottom: 33rpx;\r\n\t\t\tbackground-color: #F7F7F7;\r\n\t\t}\r\n\t\t.answer-title {\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 0 25rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 32rpx;\r\n\t\t\t.value {\r\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #000000;\n\t\t\t}\r\n\t\t\t.all-answer {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #464646;\n\t\t\t\tmargin-left: 10rpx;\n\t\t\t}\r\n\t\t}\r\n\t\t.answer-list {\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\tpadding: 0 25rpx;\r\n\t\t\t.answer-li {\r\n\t\t\t\twidth: 338rpx;\r\n\t\t\t\theight: 153rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tbackground-color: #FFFFFF;\r\n\t\t\t\tborder-radius: 29rpx;\r\n\t\t\t\tpadding-left: 35rpx;\r\n\t\t\t\tmargin-bottom: 13rpx;\r\n\t\t\t\ttransition: all 100ms cubic-bezier(.36, .66, .04, 1);\r\n\t\t\t\tbox-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);\r\n\t\t\t\t&:active {\r\n\t\t\t\t\tbackground-color: #fcfcfc;\r\n\t\t\t\t}\r\n\t\t\t\t.icon {\n\t\t\t\t\twidth: 98rpx;\n\t\t\t\t\theight: 98rpx;\r\n\t\t\t\t\tmargin-right: 16rpx;\n\t\t\t\t}\r\n\t\t\t\t.title-schedule {\r\n\t\t\t\t\theight: 98rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t.title {\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tcolor: #464646;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.schedule {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tmargin-top: 17rpx;\r\n\t\t\t\t\t\t.label {\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: #B2B2B2;\r\n\t\t\t\t\t\t\t.margin {\r\n\t\t\t\t\t\t\t\tmargin: 0 5rpx;\r\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t.value {\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: #F4621A;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./answer.vue?vue&type=style&index=0&id=6f48d1d8&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./answer.vue?vue&type=style&index=0&id=6f48d1d8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749956143987\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}