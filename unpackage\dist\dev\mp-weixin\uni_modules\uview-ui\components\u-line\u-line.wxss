.flex.data-v-727e452e {
  display: flex;
}
.column.data-v-727e452e {
  flex-direction: column;
}
.center.data-v-727e452e {
  align-items: center;
}
.space-between.data-v-727e452e {
  justify-content: space-between;
}
view.data-v-727e452e, scroll-view.data-v-727e452e, swiper-item.data-v-727e452e {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-line.data-v-727e452e {
  vertical-align: middle;
}
