.flex.data-v-09988a29 {
  display: flex;
}
.column.data-v-09988a29 {
  flex-direction: column;
}
.center.data-v-09988a29 {
  align-items: center;
}
.space-between.data-v-09988a29 {
  justify-content: space-between;
}
view.data-v-09988a29, scroll-view.data-v-09988a29, swiper-item.data-v-09988a29 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-textarea.data-v-09988a29 {
  border-radius: 4px;
  background-color: #fff;
  position: relative;

  display: flex;

  flex-direction: row;
  flex: 1;
  padding: 9px;
}
.u-textarea--radius.data-v-09988a29 {
  border-radius: 4px;
}
.u-textarea--no-radius.data-v-09988a29 {
  border-radius: 0;
}
.u-textarea--disabled.data-v-09988a29 {
  background-color: #f5f7fa;
}
.u-textarea__field.data-v-09988a29 {
  flex: 1;
  font-size: 15px;
  color: #606266;
  width: 100%;
}
.u-textarea__count.data-v-09988a29 {
  position: absolute;
  right: 5px;
  bottom: 2px;
  font-size: 12px;
  color: #909193;
  background-color: #ffffff;
  padding: 1px 4px;
}
