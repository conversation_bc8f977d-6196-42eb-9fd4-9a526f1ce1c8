{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/index.vue?37e1", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/index.vue?79fb", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/index.vue?bc52", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/index.vue?2baf", "uni-app:///pages/index/index.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/index.vue?ca38", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/index.vue?9944"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "quickLogin", "data", "filters", "methods", "onList", "uni", "url", "openPrivacyContract", "onLoad", "onReady", "onShow", "onHide", "onUnload", "onReachBottom", "onPullDownRefresh", "onBackPress", "onPageScroll", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,+rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC8D9rB;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IAAA,QAEA;EAAA;EACAC;EACAC;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACAF;IACA;EAEA;EACA;EACAG,kCAEA;EACA;EACAC;EACA;EACAC,2BAEA;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC/GA;AAAA;AAAA;AAAA;AAAyyC,CAAgB,swCAAG,EAAC,C;;;;;;;;;;;ACA7zC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-navbar/u-navbar\" */ \"@/uni_modules/uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-image/u-image\" */ \"@/uni_modules/uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"index-content\">\r\n\t\t<u-navbar :placeholder=\"true\" bgColor=\"#FFD101\">\r\n\t\t\t<view class=\"u-nav-slot\" slot=\"left\">题小萌</view>\r\n\t\t</u-navbar>\r\n\t\t<view class=\"activation-item\">\r\n\t\t\t<text class=\"item-title\">已激活</text>\r\n\t\t\t<view class=\"item-list\">\r\n\t\t\t\t<view class=\"item-li\" v-for=\"(item, index) in 1\" :key=\"index\" @click=\"onList(item)\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"173rpx\" height=\"173rpx\" radius=\"26rpx\" src=\"@/static/images/1.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"value\">L系列</text>\r\n\t\t\t\t\t<text class=\"label\">题数 11225</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"placeholder\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"activation-item position-relative\">\r\n\t\t\t<text class=\"item-title\">未激活</text>\r\n\t\t\t<view class=\"item-list r-item-list\">\r\n\t\t\t\t<view class=\"list-title\">\r\n\t\t\t\t\t<text class=\"title\">与豆伴匠配套</text>\r\n\t\t\t\t\t<text class=\"icon\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item-li\" @click=\"onList()\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"173rpx\" height=\"173rpx\" radius=\"26rpx\" src=\"@/static/images/2.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"value\">L系列</text>\r\n\t\t\t\t\t<text class=\"label\">题数 11225</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item-li\" @click=\"onList()\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"173rpx\" height=\"173rpx\" radius=\"26rpx\" src=\"@/static/images/3.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"value\">R系列</text>\r\n\t\t\t\t\t<text class=\"label\">题数 12325</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item-li\" @click=\"onList()\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"173rpx\" height=\"173rpx\" radius=\"26rpx\" src=\"@/static/images/4.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"value\">A系列</text>\r\n\t\t\t\t\t<text class=\"label\">题数 4525</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item-li\" @click=\"onList()\">\r\n\t\t\t\t\t<view class=\"image\">\r\n\t\t\t\t\t\t<u-image width=\"173rpx\" height=\"173rpx\" radius=\"26rpx\" src=\"@/static/images/5.png\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"value\">H系列</text>\r\n\t\t\t\t\t<text class=\"label\">题数 3865</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"placeholder\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<quick-login ref=\"login\" @loginSuccess=\"loginSuccess\" />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport quickLogin from '@/components/quickLogin.vue'\r\n\timport { userCouponStatus, updateCouponStatus, receivableCoupon, receiveAllCoupon, normalCouponStatus } from '@/api/index'\r\n\timport { wxGetOpenId } from '@/api/login'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tquickLogin\r\n\t\t},\r\n\t\tdata: () => ({\r\n\t\t\t\r\n\t\t}),\r\n\t\tfilters: {},\r\n\t\tmethods: {\r\n\t\t\t// 跳转题库列表\r\n\t\t\tonList(e) {\r\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/index/list'\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 跳转至隐私协议页面\r\n\t\t\topenPrivacyContract() {\r\n\t\t\t\tuni.openPrivacyContract()\r\n\t\t\t},\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面加载\r\n\t\tonLoad(options) {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面初次渲染完成\r\n\t\tonReady() {},\r\n\t\t// 页面周期函数--监听页面显示(not-nvue)\r\n\t\tonShow() {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 页面周期函数--监听页面隐藏\r\n\t\tonHide() {},\r\n\t\t// 页面周期函数--监听页面卸载\r\n\t\tonUnload() {},\r\n\t\t// 页面处理函数--监听用户上拉触底\r\n\t\tonReachBottom() {},\r\n\t\t// 页面处理函数--监听用户下拉动作\r\n\t\tonPullDownRefresh() {},\r\n\t\t// 页面周期函数--监听页面返回\r\n\t\tonBackPress() {},\r\n\t\t// 页面处理函数--监听页面滚动(not-nvue)\r\n\t\tonPageScroll(e) {},\r\n\t\t// 页面处理函数--用户点击右上角分享好友\r\n\t\tonShareAppMessage(options) {},\r\n\t\t// 页面处理函数--用户点击右上角分享朋友圈\r\n\t\tonShareTimeline(options) {}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.index-content {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 100vh;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tbackground-image: linear-gradient(to bottom, #FFD101 10%, #FFF 50%);\r\n\t\t.u-nav-slot {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #000000;\r\n\t\t}\r\n\t\t.activation-item {\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tmargin: 15rpx 0 0;\r\n\t\t\t&.position-relative {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tbottom: 100rpx;\r\n\t\t\t}\r\n\t\t\t.item-title {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #000000;\r\n\t\t\t\tbackground-color: #FFF3E0;\r\n\t\t\t\tborder-radius: 43rpx 43rpx 0 0;\r\n\t\t\t\tpadding: 24rpx 0 0;\n\t\t\t}\r\n\t\t\t.item-list {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\tpadding: 50rpx 40rpx 70rpx;\r\n\t\t\t\tbackground-color: #FFF;\r\n\t\t\t\tborder-radius: 43rpx 43rpx 0 0;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tbottom: 32rpx;\r\n\t\t\t\t&.r-item-list {\r\n\t\t\t\t\tpadding-top: 22rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.placeholder {\r\n\t\t\t\t\twidth: 173rpx;\n\t\t\t\t\theight: 0rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.list-title {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-bottom: 25rpx;\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\tcolor: #000000;\r\n\t\t\t\t\t\tz-index: 10;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.icon {\r\n\t\t\t\t\t\twidth: 126rpx;\r\n\t\t\t\t\t\theight: 15rpx;\r\n\t\t\t\t\t\tbackground-image: linear-gradient(to right, #FFD101 0%, #FFFFFF 100%);\r\n\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tbottom: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.item-li {\r\n\t\t\t\t\twidth: 173rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-bottom: 25rpx;\r\n\t\t\t\t\ttransition: all 100ms cubic-bezier(.36, .66, .04, 1);\r\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.99);\n\t\t\t\t\t}\r\n\t\t\t\t\t.image {\r\n\t\t\t\t\t\twidth: 173rpx;\r\n\t\t\t\t\t\theight: 173rpx;\r\n\t\t\t\t\t\tborder-radius: 26rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.value {\r\n\t\t\t\t\t\tfont-size: 31rpx;\r\n\t\t\t\t\t\tcolor: #464646;\r\n\t\t\t\t\t\tmargin: 18rpx 0 9rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.label {\r\n\t\t\t\t\t\tfont-size: 29rpx;\r\n\t\t\t\t\t\tcolor: #B2B2B2;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749956144001\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}