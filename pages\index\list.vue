<template>
	<view class="list-content">
		<u-navbar :placeholder="true" bgColor="#FFD101" :autoBack="true">
			<view class="u-nav-slot" slot="left"><image class="arrow" src="@/static/images/arrow-left.png" /> L系列</view>
		</u-navbar>
		<view class="question-list">
			<text class="list-title">与豆伴匠配套题库 - L系列</text>
			<view class="question-li">
				<view class="left-item">
					<view class="image">
						<u-image width="130rpx" height="130rpx" radius="47rpx" src="@/static/images/1.png" />
					</view>
					<view class="title-schedule">
						<text class="title">L1</text>
						<view class="schedule">
							<text class="label">题数：</text>
							<text class="value">0</text>
							<text class="label margin">/</text>
							<text class="label">2359</text>
						</view>
					</view>
				</view>
				<button class="u-reset-button answer" @click="onAnswer">准备答题</button>
			</view>
			<view class="question-li">
				<view class="left-item">
					<view class="image">
						<u-image width="130rpx" height="130rpx" radius="47rpx" src="@/static/images/2.png" />
					</view>
					<view class="title-schedule">
						<text class="title">L2</text>
						<view class="schedule">
							<text class="label">题数：</text>
							<text class="value">0</text>
							<text class="label margin">/</text>
							<text class="label">2359</text>
						</view>
					</view>
				</view>
				<button class="u-reset-button answer">准备答题</button>
			</view>
			<view class="question-li">
				<view class="left-item">
					<view class="image">
						<u-image width="130rpx" height="130rpx" radius="47rpx" src="@/static/images/3.png" />
					</view>
					<view class="title-schedule">
						<text class="title">L3</text>
						<view class="schedule">
							<text class="label">题数：</text>
							<text class="value">200</text>
							<text class="label margin">/</text>
							<text class="label">5432</text>
						</view>
					</view>
				</view>
				<button class="u-reset-button answer">准备答题</button>
			</view>
			<view class="question-li">
				<view class="left-item">
					<view class="image">
						<u-image width="130rpx" height="130rpx" radius="47rpx" src="@/static/images/4.png" />
					</view>
					<view class="title-schedule">
						<text class="title">L4</text>
						<view class="schedule">
							<text class="label">题数：</text>
							<text class="value">0</text>
							<text class="label margin">/</text>
							<text class="label">12359</text>
						</view>
					</view>
				</view>
				<button class="u-reset-button answer">准备答题</button>
			</view>
			<view class="question-li">
				<view class="left-item">
					<view class="image">
						<u-image width="130rpx" height="130rpx" radius="47rpx" src="@/static/images/5.png" />
					</view>
					<view class="title-schedule">
						<text class="title">L5</text>
						<view class="schedule">
							<text class="label">题数：</text>
							<text class="value">33</text>
							<text class="label margin">/</text>
							<text class="label">5359</text>
						</view>
					</view>
				</view>
				<button class="u-reset-button answer">准备答题</button>
			</view>
			<view class="question-li">
				<view class="left-item">
					<view class="image">
						<u-image width="130rpx" height="130rpx" radius="47rpx" src="@/static/images/6.png" />
					</view>
					<view class="title-schedule">
						<text class="title">L6</text>
						<view class="schedule">
							<text class="label">题数：</text>
							<text class="value">0</text>
							<text class="label margin">/</text>
							<text class="label">2359</text>
						</view>
					</view>
				</view>
				<button class="u-reset-button not-activation">待激活</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		components: {},
		data: () => ({
			
		}),
		filters: {},
		methods: {
			// 答题
			onAnswer() {
				// 跳转至答题页面
				uni.navigateTo({
					url: '/pages/index/answer'
				});
			},
		},
		// 页面周期函数--监听页面加载
		onLoad(options) {
			
		},
		// 页面周期函数--监听页面初次渲染完成
		onReady() {},
		// 页面周期函数--监听页面显示(not-nvue)
		onShow() {
			
		},
		// 页面周期函数--监听页面隐藏
		onHide() {},
		// 页面周期函数--监听页面卸载
		onUnload() {},
		// 页面处理函数--监听用户上拉触底
		onReachBottom() {},
		// 页面处理函数--监听用户下拉动作
		onPullDownRefresh() {},
		// 页面周期函数--监听页面返回
		onBackPress() {},
		// 页面处理函数--监听页面滚动(not-nvue)
		onPageScroll(e) {},
		// 页面处理函数--用户点击右上角分享好友
		onShareAppMessage(options) {},
		// 页面处理函数--用户点击右上角分享朋友圈
		onShareTimeline(options) {}
	};
</script>
<style lang="scss" scoped>
	.list-content {
		width: 100%;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		background-image: linear-gradient(to bottom, #FFD101 10%, #FFF 50%);
		.u-nav-slot {
			font-size: 36rpx;
			font-weight: 600;
			color: #000000;
			display: flex;
			align-items: center;
			.arrow {
				width: 30rpx;
				height: 30rpx;
				margin-right: 10rpx;
			}
		}
		.question-list {
			width: 100%;
			display: flex;
			flex-direction: column;
			padding: 55rpx 60rpx;
			border-radius: 43rpx 43rpx 0 0;
			background-color: #FFFFFF;
			margin-top: 15rpx;
			.list-title {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 36rpx;
				color: #000000;
				margin-bottom: 35rpx;
			}
			.question-li {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 16rpx 0;
				.left-item {
					display: flex;
					align-items: center;
					.image {
						width: 130rpx;
						heeight: 130rpx;
					}
					.title-schedule {
						height: 130rpx;
						display: flex;
						flex-direction: column;
						justify-content: center;
						margin-left: 29rpx;
						.title {
							font-size: 31rpx;
							font-weight: 600;
							color: #464646;
							margin-bottom: 22rpx;
						}
						.schedule {
							display: flex;
							align-items: center;
							.label {
								font-size: 24rpx;
								color: #464646;
								&.margin {
									margin: 0 5rpx;
								}
							}
							.value {
								color: #F4621A;
								font-size: 24rpx;
							}
						}
					}
				}
				.answer {
					width: 182rpx;
					height: 48rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 26rpx;
					color: #000000;
					background-color: #FFD101;
					border-radius: 24rpx;
					margin: 0;
					transition: all 100ms cubic-bezier(.36, .66, .04, 1);
					box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);
					&:active {
						background-color: #ebc000;
					}
				}
				.not-activation {
					width: 182rpx;
					height: 48rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 26rpx;
					color: #000000;
					background-color: #D6D6D6;
					border-radius: 24rpx;
					margin: 0;
					transition: all 100ms cubic-bezier(.36, .66, .04, 1);
					box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(214, 214, 214, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(214, 214, 214, 0.2);
					&:active {
						background-color: #c3c3c3;
					}
				}
			}
		}
	}
</style>