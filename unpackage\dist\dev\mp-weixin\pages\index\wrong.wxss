.flex.data-v-38014372 {
  display: flex;
}
.column.data-v-38014372 {
  flex-direction: column;
}
.center.data-v-38014372 {
  align-items: center;
}
.space-between.data-v-38014372 {
  justify-content: space-between;
}
.wrong-content.data-v-38014372 {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom, #FFF 10%, #FFF 50%);
}
.wrong-content .u-nav-slot.data-v-38014372 {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
  display: flex;
  align-items: center;
}
.wrong-content .u-nav-slot .arrow.data-v-38014372 {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}
.wrong-content .wrong-list.data-v-38014372 {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 38rpx 20rpx;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 10rpx;
  border-radius: 43rpx 43rpx 0 0;
  position: relative;
  bottom: 33rpx;
}
.wrong-content .wrong-list .wrong-title.data-v-38014372 {
  width: 100%;
  font-size: 30rpx;
  color: #464646;
  padding-bottom: 33rpx;
  border-bottom: 1rpx solid #E8E8E8;
}
.wrong-content .wrong-list .wrong-li.data-v-38014372 {
  width: 100%;
  padding: 35rpx 0;
  border-bottom: 1rpx solid #E8E8E8;
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.wrong-content .wrong-list .wrong-li.data-v-38014372:active {
  background-color: #f5f5f5;
}
.wrong-content .wrong-list .wrong-li .content.data-v-38014372 {
  font-size: 32rpx;
  color: #000000;
  line-height: 42rpx;
}
.wrong-content .position-bottom.data-v-38014372 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 25rpx 20rpx;
  background-color: #FFF;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);
  border-top: 1rpx solid #e4e7ed;
}
.wrong-content .position-bottom .button-list.data-v-38014372 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.wrong-content .position-bottom .button-list .button.data-v-38014372 {
  width: 338rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #DDDDDD;
  border-radius: 29rpx;
  transition: all 150ms cubic-bezier(0.36, 0.66, 0.04, 1);
  margin: 0;
}
.wrong-content .position-bottom .button-list .button .text.data-v-38014372 {
  font-size: 32rpx;
  font-weight: 500;
  color: #B2B2B2;
}
.wrong-content .position-bottom .button-list .button.button-s.data-v-38014372 {
  background-color: #FFD101;
  box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);
}
.wrong-content .position-bottom .button-list .button.button-s .text.data-v-38014372 {
  color: #000;
}
.wrong-content .position-bottom .button-list .button.button-s.data-v-38014372:active {
  background-color: #f7ca00;
}
