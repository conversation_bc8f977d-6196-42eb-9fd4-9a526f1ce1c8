<template>
	<view class="category-selector-overlay" v-if="visible" @click="onOverlayClick">
		<view class="category-selector" @click.stop>
			<!-- 标题 -->
			<view class="selector-header">
				<text class="header-title">请选择激活内容</text>
			</view>

			<!-- 面包屑导航 -->
			<view class="breadcrumb">
				<text class="breadcrumb-label">已选：</text>
				<view class="breadcrumb-items">
					<text v-for="(item, index) in breadcrumbItems" :key="index" class="breadcrumb-item" @click="goToLevel(index)">
						{{ item.name }}
					</text>
				</view>
			</view>

			<!-- 列表容器 -->
			<view class="list-container">
				<scroll-view class="category-list" scroll-y="true" :scroll-top="scrollTop">
					<view v-for="(item, index) in currentLevelData" :key="item.id" class="category-item"
						:class="{ 'item-selected': selectedItems[currentLevel] && selectedItems[currentLevel].id === item.id }"
						@click="selectItem(item, index)">
						<text class="item-text">{{ item.name }}</text>
						<view class="item-arrow" v-if="item.children && item.children.length > 0">
							<text class="arrow-icon">></text>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 提示文字 -->
			<view class="hint-text">
				<text class="hint">请滑动选择要激活的内容</text>
			</view>

			<!-- 操作按钮 -->
			<view class="action-buttons">
				<button class="btn btn-back" @click="goBack" v-if="currentLevel > 0">返回</button>
				<button class="btn btn-next" @click="nextStep">下一步</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CategorySelector',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		data: {
			type: Array,
			default: () => []
		}
	},
	data() {
		return {
			currentLevel: 0, // 当前层级
			selectedItems: {}, // 每个层级选中的项目
			scrollTop: 0, // 滚动位置
		}
	},
	computed: {
		// 当前层级的数据
		currentLevelData() {
			if (this.currentLevel === 0) {
				return this.data;
			}

			let currentData = this.data;
			for (let i = 0; i < this.currentLevel; i++) {
				const selectedItem = this.selectedItems[i];
				if (selectedItem && selectedItem.children) {
					currentData = selectedItem.children;
				} else {
					return [];
				}
			}
			return currentData;
		},

		// 面包屑数据
		breadcrumbItems() {
			const items = [];
			for (let i = 0; i <= this.currentLevel; i++) {
				if (this.selectedItems[i]) {
					items.push(this.selectedItems[i]);
				}
			}
			return items;
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.resetSelector();
			}
		}
	},
	methods: {
		// 选择项目
		selectItem(item) {
			// 设置当前层级的选中项
			this.$set(this.selectedItems, this.currentLevel, item);

			// 清除后续层级的选择
			const keysToDelete = Object.keys(this.selectedItems).filter(key => parseInt(key) > this.currentLevel);
			keysToDelete.forEach(key => {
				this.$delete(this.selectedItems, key);
			});

			// 如果有子级，自动进入下一级
			if (item.children && item.children.length > 0) {
				setTimeout(() => {
					this.currentLevel++;
					this.scrollTop = 0;
				}, 150);
			}
		},

		// 返回上一级
		goBack() {
			if (this.currentLevel > 0) {
				this.currentLevel--;
				this.scrollTop = 0;

				// 清除当前层级及后续层级的选择
				const keysToDelete = Object.keys(this.selectedItems).filter(key => parseInt(key) >= this.currentLevel);
				keysToDelete.forEach(key => {
					this.$delete(this.selectedItems, key);
				});
			}
		},

		// 跳转到指定层级
		goToLevel(level) {
			if (level < this.currentLevel) {
				this.currentLevel = level;
				this.scrollTop = 0;

				// 清除后续层级的选择
				const keysToDelete = Object.keys(this.selectedItems).filter(key => parseInt(key) > level);
				keysToDelete.forEach(key => {
					this.$delete(this.selectedItems, key);
				});
			}
		},

		// 下一步
		nextStep() {
			const selectedPath = this.getSelectedPath();
			if (selectedPath.length === 0) {
				uni.showToast({
					title: '请选择内容',
					icon: 'none'
				});
				return;
			}

			this.$emit('confirm', {
				selectedItems: this.selectedItems,
				selectedPath: selectedPath,
				finalSelection: this.selectedItems[this.currentLevel]
			});
			this.closeSelector();
		},

		// 获取选择路径
		getSelectedPath() {
			const path = [];
			for (let i = 0; i <= this.currentLevel; i++) {
				if (this.selectedItems[i]) {
					path.push(this.selectedItems[i]);
				}
			}
			return path;
		},

		// 重置选择器
		resetSelector() {
			this.currentLevel = 0;
			this.selectedItems = {};
			this.scrollTop = 0;
		},

		// 关闭选择器
		closeSelector() {
			this.$emit('close');
		},

		// 点击遮罩层
		onOverlayClick() {
			this.closeSelector();
		}
	}
}
</script>

<style lang="scss" scoped>
.category-selector-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.category-selector {
	width: 600rpx;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	margin: 0 40rpx;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}

// 标题
.selector-header {
	text-align: center;
	margin-bottom: 30rpx;

	.header-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
}

// 面包屑
.breadcrumb {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	min-height: 40rpx;

	.breadcrumb-label {
		font-size: 28rpx;
		color: #666;
		margin-right: 10rpx;
	}

	.breadcrumb-items {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.breadcrumb-item {
		font-size: 28rpx;
		color: #FFD101;
		margin-right: 10rpx;
		cursor: pointer;

		&:not(:last-child)::after {
			content: ' - ';
			color: #999;
			margin-left: 10rpx;
		}

		&:hover {
			text-decoration: underline;
		}
	}
}

// 列表容器
.list-container {
	flex: 1;
	margin-bottom: 20rpx;
	border: 2rpx solid #E5E5E5;
	border-radius: 15rpx;
	overflow: hidden;
}

.category-list {
	height: 400rpx;
	background-color: #F8F8F8;
}

.category-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 25rpx 30rpx;
	background-color: #FFFFFF;
	border-bottom: 1rpx solid #E5E5E5;
	transition: all 0.2s ease;

	&:last-child {
		border-bottom: none;
	}

	&.item-selected {
		background-color: #FFF8E1;
		border-left: 6rpx solid #FFD101;
	}

	&:active {
		background-color: #F5F5F5;
	}

	.item-text {
		font-size: 32rpx;
		color: #333;
		flex: 1;
	}

	.item-arrow {
		width: 30rpx;
		height: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.arrow-icon {
			font-size: 24rpx;
			color: #999;
			font-weight: bold;
		}
	}
}

// 提示文字
.hint-text {
	text-align: center;
	margin-bottom: 30rpx;

	.hint {
		font-size: 26rpx;
		color: #999;
	}
}

// 操作按钮
.action-buttons {
	display: flex;
	gap: 20rpx;

	.btn {
		flex: 1;
		height: 80rpx;
		border-radius: 15rpx;
		font-size: 32rpx;
		font-weight: 600;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.2s ease;

		&.btn-back {
			background-color: #F5F5F5;
			color: #666;

			&:active {
				background-color: #E5E5E5;
			}
		}

		&.btn-next {
			background-color: #FFD101;
			color: #333;

			&:active {
				background-color: #E6BC00;
			}
		}
	}
}
</style>
