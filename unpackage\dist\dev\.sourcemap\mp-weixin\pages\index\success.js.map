{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/success.vue?e669", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/success.vue?2cd0", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/success.vue?6c40", "uni-app:///pages/index/success.vue", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/success.vue?573a", "webpack:///E:/KingToyo/app_tixiaomeng/pages/index/success.vue?c7b4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "answeredCount", "correctCount", "wrongCount", "score", "animatedScore", "progressRotation", "animationTimer", "computed", "methods", "startProgressAnimation", "currentStep", "clearInterval", "checkOrder", "uni", "url", "returnHome", "delta", "getInviteInfo", "invitation", "code", "console", "icon", "title", "duration", "openClose", "watch", "onLoad", "onReady", "onShow", "setTimeout", "onHide", "onUnload", "onPullDownRefresh", "onReachBottom", "onBackPress", "onShareAppMessage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACoL;AACpL,gBAAgB,6LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4qB,CAAgB,isBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoDhsB;EACAC;EACAC;IAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EAAA;;EACAC;EACAC;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MAEA;QACAC;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACAC;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAF;UAAAG;QAAA;MACA;QACAH;UACAC;QACA;MACA;IACA;IACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAA;gBAAAC;gBAAApB;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAqB;gBACAP;kBACAQ;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MACA;MACA;MACA;QACA;QACAX;UAAAG;QAAA;MACA;QACAH;UAAAC;QAAA;MACA;IACA;EACA;EACAW;EACA;EACAC;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;MACA;IACA;EACA;EACA;EACAC;EACA;EACAC;IAAA;IACA;IACAC;MACA;IACA;EACA;EACA;EACAC;EACA;EACAC;IACA;IACA;MACApB;IACA;EACA;EACA;EACAqB;EACA;EACAC;EACA;EACAC;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;AC1LA;AAAA;AAAA;AAAA;AAA2yC,CAAgB,wwCAAG,EAAC,C;;;;;;;;;;;ACA/zC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/success.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/success.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./success.vue?vue&type=template&id=0caffd1d&scoped=true&\"\nvar renderjs\nimport script from \"./success.vue?vue&type=script&lang=js&\"\nexport * from \"./success.vue?vue&type=script&lang=js&\"\nimport style0 from \"./success.vue?vue&type=style&index=0&id=0caffd1d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0caffd1d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/success.vue\"\nexport default component.exports", "export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./success.vue?vue&type=template&id=0caffd1d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./success.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./success.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"success-content\">\r\n\t\t<!-- 顶部标题 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<text class=\"title\">太棒了!</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 成绩展示区域 -->\r\n\t\t<view class=\"score-section\">\r\n\t\t\t<!-- 半圆形进度条 -->\r\n\t\t\t<view class=\"progress-container\">\r\n\t\t\t\t<view class=\"progress-circle\">\r\n\t\t\t\t\t<!-- 背景圆弧 -->\r\n\t\t\t\t\t<view class=\"progress-bg\"></view>\r\n\t\t\t\t\t<!-- 进度圆弧 -->\r\n\t\t\t\t\t<view class=\"progress-bar\" :style=\"{ transform: `rotate(${progressRotation}deg)` }\"></view>\r\n\t\t\t\t\t<!-- 进度圆点 -->\r\n\t\t\t\t\t<view class=\"progress-dot\" :style=\"{ transform: `rotate(${progressRotation}deg)` }\"></view>\r\n\t\t\t\t\t<!-- 中心内容 -->\r\n\t\t\t\t\t<view class=\"progress-center\">\r\n\t\t\t\t\t\t<text class=\"score-text\">{{ animatedScore }}%</text>\r\n\t\t\t\t\t\t<text class=\"score-label\">正确率</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 统计信息 -->\r\n\t\t\t<view class=\"stats-container\">\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-number\">{{ answeredCount }}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">答题数</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-number correct\">{{ correctCount }}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">答对</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stats-item\">\r\n\t\t\t\t\t<text class=\"stats-number wrong\">{{ wrongCount }}</text>\r\n\t\t\t\t\t<text class=\"stats-label\">答错</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 操作按钮 -->\r\n\t\t<view class=\"action-buttons\">\r\n\t\t\t<button class=\"btn btn-primary\" @click=\"checkOrder\">进入错题本</button>\r\n\t\t\t<button class=\"btn btn-secondary\" @click=\"returnHome\">返回</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tcomponents: {},\r\n\tdata: () => ({\r\n\t\tansweredCount: '50', // 答题数量\r\n\t\tcorrectCount: '45', // 正确数\r\n\t\twrongCount: '5', // 错题数\r\n\t\tscore: '95', // 正确率\r\n\t\tanimatedScore: 0, // 动画中的分数\r\n\t\tprogressRotation: -90, // 进度条旋转角度（从-90度开始，即左侧）\r\n\t\tanimationTimer: null, // 动画定时器\r\n\t}),\r\n\tcomputed: {},\r\n\tmethods: {\r\n\t\t// 开始进度条动画\r\n\t\tstartProgressAnimation() {\r\n\t\t\t// 确保score是0-100之间的整数\r\n\t\t\tconst targetScore = Math.max(0, Math.min(100, parseInt(this.score) || 0));\r\n\t\t\tconst duration = 2000; // 动画持续时间2秒\r\n\t\t\tconst steps = 60; // 动画步数\r\n\t\t\tconst stepDuration = duration / steps;\r\n\t\t\tconst scoreStep = targetScore / steps;\r\n\r\n\t\t\tlet currentStep = 0;\r\n\r\n\t\t\tthis.animationTimer = setInterval(() => {\r\n\t\t\t\tcurrentStep++;\r\n\r\n\t\t\t\t// 更新分数\r\n\t\t\t\tthis.animatedScore = Math.round(scoreStep * currentStep);\r\n\r\n\t\t\t\t// 更新进度条角度\r\n\t\t\t\t// 半圆从左侧(-90度)到右侧(90度)，总共180度\r\n\t\t\t\t// 根据分数百分比计算角度：-90 + (score/100) * 180\r\n\t\t\t\tconst currentScore = scoreStep * currentStep;\r\n\t\t\t\tthis.progressRotation = -90 + (currentScore / 100) * 180;\r\n\r\n\t\t\t\t// 动画完成\r\n\t\t\t\tif (currentStep >= steps) {\r\n\t\t\t\t\tclearInterval(this.animationTimer);\r\n\t\t\t\t\tthis.animatedScore = targetScore;\r\n\t\t\t\t\tthis.progressRotation = -90 + (targetScore / 100) * 180;\r\n\t\t\t\t}\r\n\t\t\t}, stepDuration);\r\n\t\t},\r\n\r\n\t\t// 进入错题本\r\n\t\tcheckOrder() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/index/wrongBook'\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 返回\r\n\t\treturnHome() {\r\n\t\t\t// 返回到首页或上一页\r\n\t\t\tlet pages = getCurrentPages();\r\n\t\t\tif (pages.length > 1) {\r\n\t\t\t\tuni.navigateBack({ delta: 1 });\r\n\t\t\t} else {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取邀请信息\r\n\t\tasync getInviteInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tconst { code, data } = await invitation({})\r\n\t\t\t\tif (code === '00000') {\r\n\t\t\t\t\tthis.inviteInfo = data\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取邀请信息失败:', error)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '获取邀请信息失败',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 取消返回上一页\r\n\t\topenClose() {\r\n\t\t\tlet pages = getCurrentPages();\r\n\t\t\tlet currentPages = pages.length\r\n\t\t\tif (currentPages > 1) {\r\n\t\t\t\tlet beforePage = pages[pages.length - 2]\r\n\t\t\t\tuni.navigateBack({ delta: 1 })\r\n\t\t\t} else {\r\n\t\t\t\tuni.switchTab({ url: \"/pages/index/index\" });\r\n\t\t\t}\r\n\t\t},\r\n\t},\r\n\twatch: {},\r\n\t// 页面周期函数--监听页面加载\r\n\tonLoad(options) {\r\n\t\tif (options.a) {\r\n\t\t\tthis.answeredCount = options.a\r\n\t\t}\r\n\t\tif (options.c) {\r\n\t\t\tthis.correctCount = options.c\r\n\t\t}\r\n\t\tif (options.w) {\r\n\t\t\tthis.wrongCount = options.w\r\n\t\t}\r\n\t\tif (options.s) {\r\n\t\t\t// 确保score是0-100之间的整数\r\n\t\t\tthis.score = Math.max(0, Math.min(100, parseInt(options.s) || 0)).toString()\r\n\t\t}\r\n\t},\r\n\t// 页面周期函数--监听页面初次渲染完成\r\n\tonReady() { },\r\n\t// 页面周期函数--监听页面显示(not-nvue)\r\n\tonShow() {\r\n\t\t// 延迟启动动画，让页面先渲染完成\r\n\t\tsetTimeout(() => {\r\n\t\t\tthis.startProgressAnimation();\r\n\t\t}, 300);\r\n\t},\r\n\t// 页面周期函数--监听页面隐藏\r\n\tonHide() { },\r\n\t// 页面周期函数--监听页面卸载\r\n\tonUnload() {\r\n\t\t// 清理定时器\r\n\t\tif (this.animationTimer) {\r\n\t\t\tclearInterval(this.animationTimer);\r\n\t\t}\r\n\t},\r\n\t// 页面处理函数--监听用户下拉动作\r\n\tonPullDownRefresh() { },\r\n\t// 页面处理函数--监听用户上拉触底\r\n\tonReachBottom() { },\r\n\t// 页面周期函数--监听页面返回\r\n\tonBackPress() { },\r\n\t// 页面处理函数--用户点击右上角分享好友\r\n\tonShareAppMessage(options) { },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.success-content {\r\n\twidth: 100%;\r\n\tmin-height: 100vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tbox-sizing: border-box;\r\n\r\n\t// 顶部标题\r\n\t.header {\r\n\t\twidth: 100%;\r\n\t\theight: 200rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground-image: linear-gradient(to bottom, #FFD101, #FFFFFF);\r\n\r\n\t\t.title {\r\n\t\t\tfont-size: 60rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t}\r\n\r\n\t// 成绩展示区域\r\n\t.score-section {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 120rpx;\r\n\t}\r\n\r\n\t// 半圆形进度条容器\r\n\t.progress-container {\r\n\t\tmargin-bottom: 80rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t// 进度圆环\r\n\t.progress-circle {\r\n\t\tposition: relative;\r\n\t\twidth: 416rpx;\r\n\t\theight: 208rpx;\r\n\r\n\t\t// 背景圆弧\r\n\t\t.progress-bg {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 416rpx;\r\n\t\t\theight: 416rpx;\r\n\t\t\tborder: 20rpx solid #FFED9C;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tborder-bottom: 20rpx solid transparent;\r\n\t\t\tclip: rect(0, 416rpx, 208rpx, 0);\r\n\t\t}\r\n\r\n\t\t// 进度圆弧\r\n\t\t.progress-bar {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 416rpx;\r\n\t\t\theight: 416rpx;\r\n\t\t\tborder: 20rpx solid #FFD101;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tborder-bottom: 20rpx solid transparent;\r\n\t\t\tclip: rect(0, 416rpx, 208rpx, 0);\r\n\t\t\ttransform-origin: center center;\r\n\t\t\ttransition: transform 0.1s ease-out;\r\n\t\t}\r\n\r\n\t\t// 进度圆点\r\n\t\t.progress-dot {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 10rpx;\r\n\t\t\tright: 198rpx;\r\n\t\t\twidth: 20rpx;\r\n\t\t\theight: 20rpx;\r\n\t\t\tbackground-color: #FFD101;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\ttransform-origin: 198rpx 198rpx;\r\n\t\t\ttransition: transform 0.1s ease-out;\r\n\t\t\tz-index: 5;\r\n\t\t}\r\n\r\n\t\t// 中心内容\r\n\t\t.progress-center {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 50%;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translate(-50%, -20%);\r\n\t\t\ttext-align: center;\r\n\t\t\tz-index: 10;\r\n\r\n\t\t\t.score-text {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 80rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #FFD101;\r\n\t\t\t\tline-height: 1;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.score-label {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 进度条标记\r\n\t.progress-container {\r\n\t\tposition: relative;\r\n\r\n\t\t&::before {\r\n\t\t\tcontent: '0';\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: -40rpx;\r\n\t\t\tleft: 20rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\r\n\t\t&::after {\r\n\t\t\tcontent: '100';\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: -40rpx;\r\n\t\t\tright: 20rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n\r\n\t// 统计信息容器\r\n\t.stats-container {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\twidth: 100%;\r\n\t\tmax-width: 600rpx;\r\n\r\n\t\t.stats-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.stats-number {\r\n\t\t\t\tfont-size: 60rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #FFD101;\r\n\t\t\t\tline-height: 1;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\r\n\t\t\t\t&.correct {\r\n\t\t\t\t\tcolor: #4CAF50;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.wrong {\r\n\t\t\t\t\tcolor: #FF5722;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.stats-label {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 操作按钮\r\n\t.action-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\twidth: 100%;\r\n\t\tmax-width: 600rpx;\r\n\t\tgap: 30rpx;\r\n\r\n\t\t.btn {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100rpx;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tborder: none;\r\n\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t&.btn-primary {\r\n\t\t\t\tbackground: linear-gradient(135deg, #FFD101 0%, #FFA000 100%);\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tbox-shadow: 0rpx 8rpx 20rpx rgba(255, 209, 1, 0.3);\r\n\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: translateY(2rpx);\r\n\t\t\t\t\tbox-shadow: 0rpx 4rpx 10rpx rgba(255, 209, 1, 0.3);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&.btn-secondary {\r\n\t\t\t\tbackground: #FFF;\r\n\t\t\t\tcolor: #FFD101;\r\n\t\t\t\tborder: 2rpx solid #FFD101;\r\n\r\n\t\t\t\t&:active {\r\n\t\t\t\t\tbackground: #FFF8E1;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./success.vue?vue&type=style&index=0&id=0caffd1d&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\Program Files\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./success.vue?vue&type=style&index=0&id=0caffd1d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1749966583945\n      var cssReload = require(\"D:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}