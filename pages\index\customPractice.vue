<template>
	<view class="select-content">
		<u-navbar :placeholder="true" bgColor="#FFD101" :autoBack="true">
			<view class="u-nav-slot" slot="left">
				<image class="arrow" src="@/static/images/arrow-left.png" /> L系列
			</view>
		</u-navbar>
		<u-sticky bgColor="transparent" :customNavHeight="44 + statusBarHeight">
			<view class="header-content">
				<view class="header-box">
					<view class="left-item" @click="toggleAllSelect">
						<image class="icon" v-if="isAllSelected" src="@/static/images/agree-icon.png" />
						<image class="icon" v-else src="@/static/images/disagree-icon.png" />
						<text class="text">全选</text>
					</view>
					<button class="u-reset-button right-item" :class="{ 'right-item-s': isNextStep }"
						@click="cancelAllSelect">取消选择</button>
				</view>
			</view>
		</u-sticky>
		<view class="select-list">
			<view class="select-li" :class="{ 'select-li-s': item.selected }" v-for="(item, index) in questionList"
				:key="item.id" @click="toggleQuestion(index)">
				<text class="value">{{ item.name }}</text>
				<image class="icon" v-if="item.selected" src="@/static/images/select-s.png" />
				<image class="icon" v-else src="@/static/images/select-n.png" />
			</view>
			<view class="placeholder"></view>
			<view class="placeholder"></view>
		</view>
		<u-safe-bottom />
		<view class="position-bottom">
			<view class="button-list">
				<button class="u-reset-button button" :class="{ 'button-s': isNextStep }" @click="onPractise(1)">
					<image class="icon" src="@/static/images/answer-icon-3.png" />
					<text class="text">顺序练习</text>
				</button>
				<button class="u-reset-button button" :class="{ 'button-s': isNextStep }" @click="onPractise(2)">
					<image class="icon" src="@/static/images/answer-icon-4.png" />
					<text class="text">顺序练习</text>
				</button>
			</view>
			<u-safe-bottom />
		</view>
	</view>
</template>

<script>
export default {
	components: {},
	data: () => ({
		statusBarHeight: 0, // 状态栏高度
		// 题目列表数据
		questionList: [
			...Array.from({ length: 100 }, (_, index) => ({
				id: index + 1,
				name: `1-${index + 1}`,
				selected: false
			}))
		],
		isAllSelected: false, // 全选状态
		isNextStep: false, // 是否进行下一步
	}),
	filters: {},
	methods: {
		// 切换单个题目选中状态
		toggleQuestion(index) {
			this.questionList[index].selected = !this.questionList[index].selected;
			this.updateAllSelectedState();
			this.updateNextStepState();
		},

		// 全选/取消全选
		toggleAllSelect() {
			this.isAllSelected = !this.isAllSelected;
			this.questionList.forEach(item => {
				item.selected = this.isAllSelected;
			});
			this.updateNextStepState();
		},

		// 取消所有选择
		cancelAllSelect() {
			this.questionList.forEach(item => {
				item.selected = false;
			});
			this.isAllSelected = false;
			this.updateNextStepState();
		},

		// 更新全选状态
		updateAllSelectedState() {
			const selectedCount = this.questionList.filter(item => item.selected).length;
			this.isAllSelected = selectedCount === this.questionList.length;
		},

		// 更新下一步按钮状态
		updateNextStepState() {
			const selectedCount = this.questionList.filter(item => item.selected).length;
			this.isNextStep = selectedCount > 0;
		},
		
		// 练习
		onPractise(e) {
			// 跳转至练习页面
			uni.navigateTo({
				url: '/pages/index/practise'
			});
		},
	},
	// 页面周期函数--监听页面加载
	onLoad(options) {
		// 状态栏高度
		this.statusBarHeight = this.$store.state.app.statusBarHeight
	},
	// 页面周期函数--监听页面初次渲染完成
	onReady() {
		// 初始化下一步按钮状态
		this.updateNextStepState()
	},
	// 页面周期函数--监听页面显示(not-nvue)
	onShow() {},
	// 页面周期函数--监听页面隐藏
	onHide() { },
	// 页面周期函数--监听页面卸载
	onUnload() { },
	// 页面处理函数--监听用户上拉触底
	onReachBottom() { },
	// 页面处理函数--监听用户下拉动作
	onPullDownRefresh() { },
	// 页面周期函数--监听页面返回
	onBackPress() { },
	// 页面处理函数--监听页面滚动(not-nvue)
	onPageScroll(e) { },
	// 页面处理函数--用户点击右上角分享好友
	onShareAppMessage(options) { },
	// 页面处理函数--用户点击右上角分享朋友圈
	onShareTimeline(options) { }
};
</script>
<style lang="scss" scoped>
.select-content {
	width: 100%;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	background-image: linear-gradient(to bottom, #FFF3E0 10%, #F7F7F7 50%);

	.u-nav-slot {
		font-size: 36rpx;
		font-weight: 600;
		color: #000000;
		display: flex;
		align-items: center;

		.arrow {
			width: 30rpx;
			height: 30rpx;
			margin-right: 10rpx;
		}
	}

	.header-content {
		width: 100%;
		display: flex;
		flex-direction: column;
		background-color: #FFD101;
		padding-top: 15rpx;
	}

	.header-box {
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 22rpx 25rpx;
		background-color: #FFF3E0;
		border-radius: 43rpx 43rpx 0 0;

		.left-item {
			height: 48rpx;
			display: flex;
			align-items: center;

			.icon {
				width: 35rpx;
				height: 35rpx;
				margin-right: 16rpx;
			}

			.text {
				font-size: 30rpx;
				color: #000000;
				line-height: 48rpx;
			}
		}

		.right-item {
			width: 182rpx;
			height: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 26rpx;
			color: #B2B2B2;
			background-color: #DDDDDD;
			border-radius: 24rpx;
			margin: 0;
			transition: all 150ms cubic-bezier(.36, .66, .04, 1);
			&.right-item-s {
				box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);
				background-color: #FFD101;
				color: #000000;
			}
		}
	}

	.select-list {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-wrap: wrap;
		padding: 16rpx 25rpx;
		background-color: #F7F7F7;
		border-radius: 43rpx 43rpx 0 0;
		margin-bottom: 180rpx;

		.placeholder {
			width: 160rpx;
			height: 0rpx;
		}

		.select-li {
			width: 160rpx;
			height: 110rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #FFFFFF;
			border-radius: 20rpx;
			margin-bottom: 11rpx;
			position: relative;
			box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);
			transition: all 150ms cubic-bezier(.36, .66, .04, 1);

			&.select-li-s {
				background-color: #FFD101;
			}

			.value {
				font-size: 30rpx;
				font-weight: 500;
				color: #000000;
				margin-right: 20rpx;
			}

			.icon {
				position: absolute;
				top: 10rpx;
				right: 10rpx;
				width: 33rpx;
				height: 33rpx;
			}
		}
	}

	.position-bottom {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 25rpx 20rpx;
		background-color: #FFF;
		box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(227, 227, 227, 0.2);
		border-top: 1rpx solid #e4e7ed;
		.button-list {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.button {
				width: 338rpx;
				height: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #DDDDDD;
				border-radius: 29rpx;
				transition: all 150ms cubic-bezier(.36, .66, .04, 1);
				margin: 0;
				.icon {
					width: 60rpx;
					height: 60rpx;
					margin-right: 15rpx;
				}

				.text {
					font-size: 32rpx;
					font-weight: 500;
					color: #B2B2B2;
				}

				&.button-s {
					background-color: #FFD101;
					box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2), 0rpx 6rpx 16rpx 0rpx rgba(255, 209, 1, 0.2);
					.text {
						color: #000;
					}
					&:active {
						background-color: #f7ca00;
					}
				}
			}
		}
	}
}
</style>